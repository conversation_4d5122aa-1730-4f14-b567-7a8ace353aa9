{"_from": "hasown@^2.0.2", "_id": "hasown@2.0.2", "_inBundle": false, "_integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "_location": "/hasown", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "hasown@^2.0.2", "name": "hasown", "escapedName": "hasown", "rawSpec": "^2.0.2", "saveSpec": null, "fetchSpec": "^2.0.2"}, "_requiredBy": ["/es-set-tostringtag", "/form-data", "/get-intrinsic"], "_resolved": "https://registry.npmmirror.com/hasown/-/hasown-2.0.2.tgz", "_shasum": "003eaf91be7adc372e84ec59dc37252cedb80003", "_spec": "hasown@^2.0.2", "_where": "D:\\node\\project\\automationTools\\node_modules\\form-data", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/hasOwn/issues"}, "bundleDependencies": false, "dependencies": {"function-bind": "^1.1.2"}, "deprecated": false, "description": "A robust, ES3 compatible, \"has own property\" predicate.", "devDependencies": {"@arethetypeswrong/cli": "^0.15.1", "@ljharb/eslint-config": "^21.1.0", "@ljharb/tsconfig": "^0.2.0", "@types/function-bind": "^1.1.10", "@types/mock-property": "^1.0.2", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "mock-property": "^1.0.3", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "homepage": "https://github.com/inspect-js/hasOwn#readme", "keywords": ["has", "hasOwnProperty", "hasOwn", "has-own", "own", "has", "property", "in", "javascript", "ecmascript"], "license": "MIT", "main": "index.js", "name": "hasown", "publishConfig": {"ignore": [".github/workflows", "test"]}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/hasOwn.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "npm run tsc", "posttest": "aud --production", "posttsc": "attw -P", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "tsc": "tsc -p .", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "testling": {"files": "test/index.js"}, "types": "index.d.ts", "version": "2.0.2"}