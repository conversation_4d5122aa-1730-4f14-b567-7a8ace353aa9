const { fetchRankingData } = require('./list.js');

async function testSummary() {
    console.log('='.repeat(60));
    console.log('🚀 七麦数据API测试摘要');
    console.log('='.repeat(60));
    
    try {
        const result = await fetchRankingData();
        
        if (result.success) {
            console.log('✅ API请求成功!');
            console.log(`📊 状态码: ${result.status}`);
            console.log(`📱 数据类型: ${typeof result.data}`);
            
            if (result.data && result.data.rankInfo) {
                console.log(`📈 排行榜数据条数: ${result.data.rankInfo.length}`);
                console.log('\n🏆 前5名应用:');
                result.data.rankInfo.slice(0, 5).forEach((app, index) => {
                    console.log(`${index + 1}. ${app.appInfo.appName} (${app.appInfo.publisher})`);
                    console.log(`   排名: ${app.rank_a.ranking} | 评分: ${app.comment.rating} | 评论数: ${app.comment.num}`);
                });
            }
            
            console.log('\n📋 API返回的主要字段:');
            if (result.data) {
                Object.keys(result.data).forEach(key => {
                    console.log(`   - ${key}: ${typeof result.data[key]}`);
                });
            }
            
        } else {
            console.log('❌ API请求失败');
            console.log(`错误类型: ${result.error}`);
            console.log(`错误信息: ${result.message}`);
        }
        
    } catch (error) {
        console.log('💥 测试过程中发生异常:', error.message);
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('测试完成 ✨');
    console.log('='.repeat(60));
}

testSummary();
