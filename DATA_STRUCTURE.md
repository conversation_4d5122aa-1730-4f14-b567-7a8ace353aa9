# 数据结构说明文档

## 概述
本文档描述了QiMai API数据获取和转换后的最终数据结构。数据已经过优化，将嵌套字段提取到顶层，并对评论数进行了"万"字转换处理。

## 文件信息
- **主数据文件**: `data/type.json`
- **备份文件**: `data/type_original_backup.json` (转换前的原始数据)
- **总记录数**: 499条
- **数据范围**: 美国iPhone财务类应用排行榜 Top 1-499
- **获取时间**: 2025-06-26
- **文件大小**: ~765KB

## 数据结构

### 根级别结构
```json
{
  "metadata": { ... },
  "rankInfo": [ ... ]
}
```

### metadata 字段说明
```json
{
  "totalRecords": 499,           // 总记录数
  "pageRange": "1-10",           // 页面范围
  "successPages": 10,            // 成功获取的页数
  "failedPages": 0,              // 失败的页数
  "fetchDate": "2025-06-26T07:12:11.167Z",  // 原始获取时间
  "genre": "6015",               // 应用类型ID (财务类)
  "country": "us",               // 国家代码
  "device": "iphone",            // 设备类型
  "transformDate": "2025-06-26T07:22:50.118Z",  // 数据转换时间
  "transformNote": "数据结构已优化：提取嵌套字段到顶层，处理评论数万字转换"
}
```

### rankInfo 数组中每个应用的字段说明

#### 🔄 转换后的顶层字段 (已优化)
| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `lastReleaseTime` | string | 最后发布时间 | "2025-06-05" |
| `appId` | number | 应用ID | 283646709 |
| `appName` | string | 应用名称 | "PayPal - Pay, Send, Save" |
| `subtitle` | string | 应用副标题 | "暂无数据" |
| `publisher` | string | 发布商 | "PayPal, Inc." |
| `icon` | string | 应用图标URL | "https://..." |
| `rating` | number | 应用评分 | 4.8 |
| `num` | number | 评论数 (已转换) | 6300000 |

#### 📊 保留的原始字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| `app_id` | number | 应用ID (与appId相同) |
| `genre_sub_id` | string | 子类型ID |
| `index` | number | 排名索引 |
| `keywordCover` | string | 关键词覆盖数 |
| `keywordCoverTop3` | string | Top3关键词覆盖数 |
| `company` | object | 公司信息 |
| `rank_a` | object | 总榜排名信息 |
| `rank_b` | object | 应用榜排名信息 |
| `rank_c` | object | 分类榜排名信息 |
| `is_ad` | boolean | 是否为广告 |
| `pageNumber` | number | 数据来源页码 |
| `fetchTime` | string | 获取时间戳 |

#### 🏆 排名信息结构 (rank_a, rank_b, rank_c)
```json
{
  "ranking": 1,        // 排名位置 (数字或"-")
  "change": 0,         // 排名变化
  "genre": "财务"      // 榜单类型
}
```

## 🔢 评论数转换规则

### 转换逻辑
1. **包含"万"字**: 去掉"万"字，数值乘以10,000
2. **不包含"万"字**: 直接转换为数字
3. **包含逗号**: 自动去除逗号分隔符
4. **无效数据**: 转换为0

### 转换示例
| 原始值 | 转换后 | 说明 |
|--------|--------|------|
| "630万" | 6300000 | 630 × 10,000 |
| "1,545万" | 15450000 | 1,545 × 10,000 |
| "4.32万" | 43200 | 4.32 × 10,000 |
| "1,234" | 1234 | 直接转换 |
| "295" | 295 | 直接转换 |

## 📈 数据统计

### 基本统计
- **总应用数**: 499个
- **数据完整性**: 100% (应用名、发布商、图标)
- **有效评分**: 97.6% (487/499)
- **有效评论数**: 97.6% (487/499)

### 评分分布
- **5.0分**: 5个应用 (1.0%)
- **4.0-4.9分**: 414个应用 (83.0%)
- **3.0-3.9分**: 27个应用 (5.4%)
- **2.0-2.9分**: 29个应用 (5.8%)
- **1.0-1.9分**: 12个应用 (2.4%)
- **0分**: 12个应用 (2.4%)

### 评论数分布
- **1000万+**: 1个应用 (0.2%)
- **500万-1000万**: 6个应用 (1.2%)
- **100万-500万**: 20个应用 (4.0%)
- **10万-100万**: 100个应用 (20.0%)
- **1万-10万**: 171个应用 (34.3%)
- **1万以下**: 201个应用 (40.3%)

## 🛠️ 相关工具脚本

1. **`check-data.js`** - 数据统计和验证
2. **`verify-transform.js`** - 转换结果验证
3. **`final-report.js`** - 完整数据报告
4. **`transform-data.js`** - 数据结构转换工具

## 📝 使用示例

### 读取数据
```javascript
const data = require('./data/type.json');
console.log(`总应用数: ${data.metadata.totalRecords}`);
```

### 访问应用信息
```javascript
const firstApp = data.rankInfo[0];
console.log(`应用名: ${firstApp.appName}`);
console.log(`发布商: ${firstApp.publisher}`);
console.log(`评分: ${firstApp.rating}`);
console.log(`评论数: ${firstApp.num.toLocaleString()}`);
```

### 筛选高评分应用
```javascript
const highRatedApps = data.rankInfo.filter(app => app.rating >= 4.5);
console.log(`4.5分以上应用: ${highRatedApps.length}个`);
```

## 🔄 版本历史

- **v1.0** (2025-06-26 07:12): 初始数据获取，原始嵌套结构
- **v2.0** (2025-06-26 07:22): 数据结构优化，字段扁平化，评论数转换

---

*最后更新: 2025-06-26*
