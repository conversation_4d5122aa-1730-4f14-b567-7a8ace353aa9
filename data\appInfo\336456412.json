{"artistViewUrl": "https://apps.apple.com/us/developer/cribasoft-llc/id321923937?uo=4", "artworkUrl60": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/ba/74/45/ba74455d-2fe8-e5d3-e431-35bd1ee252a0/StandardAppIcon-0-0-1x_U007ephone-0-1-0-85-220.png/60x60bb.jpg", "artworkUrl100": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/ba/74/45/ba74455d-2fe8-e5d3-e431-35bd1ee252a0/StandardAppIcon-0-0-1x_U007ephone-0-1-0-85-220.png/100x100bb.jpg", "ipadScreenshotUrls": [], "artworkUrl512": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/ba/74/45/ba74455d-2fe8-e5d3-e431-35bd1ee252a0/StandardAppIcon-0-0-1x_U007ephone-0-1-0-85-220.png/512x512bb.jpg", "screenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/PurpleSource221/v4/45/f3/bc/45f3bc23-a362-3689-e88c-499b2de9d6b0/b90fdbb2-149f-4faf-907b-1e2900d6766c_updated-features.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource211/v4/9f/31/92/9f319283-e440-45f2-3759-5cf5056dae5a/e106e782-1e81-4575-88ef-8a42a4cc2ab9_job-details.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource211/v4/4c/9c/5d/4c9c5db4-2c39-b732-bce6-6d2da4f7df19/82bfdedb-37a3-49ba-a509-dc3c46aa73d3_location.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource221/v4/f9/91/ab/f991abb8-2589-d970-a832-a82bb970b00b/a54c41f4-f378-4c88-a495-065012ac7f17_widgets.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource221/v4/10/2f/c7/102fc7b7-0224-d026-b382-f987b45f80ae/0e4bd89f-58cc-4441-a744-2769953345c7_jobs-list.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource211/v4/e0/6c/c2/e06cc246-5ab7-dd6c-9680-bea0e7e68921/559c75c8-e4af-47bb-b46f-6e636dee3264_edit-entry.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource221/v4/7b/43/3a/7b433ab5-7af1-fd51-9cb4-8a6149feffea/b1e8cd10-b2e1-4dce-aae5-559b7c291f09_entries-report.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource211/v4/21/75/49/217549b1-9322-4e9b-3a91-ad34ee723113/2acc1cf3-783e-4abb-8b40-796a4d8cd86b_edit-job.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource221/v4/d8/4b/5c/d84b5c77-721a-1eff-bf09-f3dabd274cfc/1e0fa4f5-f315-4b03-af5f-c70d2bed23bd_dark-mode-pay-periods.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource211/v4/99/cc/59/99cc5944-1f8e-3749-2400-700fc1f23cc4/45495806-22ed-478d-ab45-45a41c0f3b13_filters.png/392x696bb.png"], "features": [], "appletvScreenshotUrls": [], "supportedDevices": ["iPhone5s-iPhone5s", "iPadAir-iPadAir", "iPadAirCellular-iPadAirCellular", "iPadMiniRetina-iPadMiniRetina", "iPadMiniRetinaCellular-iPadMiniRetinaCellular", "iPhone6-iPhone6", "iPhone6Plus-iPhone6Plus", "iPadAir2-iPadAir2", "iPadAir2Cellular-iPadAir2Cellular", "iPadMini3-iPadMini3", "iPadMini3Cellular-iPadMini3Cellular", "iPodTouchSixthGen-iPodTouchSixthGen", "iPhone6s-iPhone6s", "iPhone6sPlus-iPhone6sPlus", "iPadMini4-iPadMini4", "iPadMini4Cellular-iPadMini4Cellular", "iPadPro-iPadPro", "iPadProCellular-iPadProCellular", "iPadPro97-iPadPro97", "iPadPro97Cellular-iPadPro97Cellular", "iPhoneSE-iPhoneSE", "iPhone7-iPhone7", "iPhone7Plus-iPhone7Plus", "iPad611-iPad611", "iPad612-iPad612", "iPad71-iPad71", "iPad72-iPad72", "iPad73-iPad73", "iPad74-iPad74", "iPhone8-iPhone8", "iPhone8Plus-iPhone8Plus", "iPhoneX-iPhoneX", "iPad75-iPad75", "iPad76-iPad76", "iPhoneXS-iPhoneXS", "iPhoneXSMax-iPhoneXSMax", "iPhoneXR-iPhoneXR", "iPad812-iPad812", "iPad834-iPad834", "iPad856-iPad856", "iPad878-iPad878", "Watch4-Watch4", "iPadMini5-iPadMini5", "iPadMini5Cellular-iPadMini5Cellular", "iPadAir3-iPadAir3", "iPadAir3Cellular-iPadAir3Cellular", "iPodTouchSeventhGen-iPodTouchSeventhGen", "iPhone11-iPhone11", "iPhone11Pro-iPhone11Pro", "iPadSeventhGen-iPadSeventhGen", "iPadSeventhGenCellular-iPadSeventhGenCellular", "iPhone11ProMax-iPhone11ProMax", "iPhoneSESecondGen-iPhoneSESecondGen", "iPadProSecondGen-iPadProSecondGen", "iPadProSecondGenCellular-iPadProSecondGenCellular", "iPadProFourthGen-iPadProFourthGen", "iPadProFourthGenCellular-iPadProFourthGenCellular", "iPhone12Mini-iPhone12Mini", "iPhone12-iPhone12", "iPhone12Pro-iPhone12Pro", "iPhone12ProMax-iPhone12ProMax", "iPadAir4-iPadAir4", "iPadAir4Cellular-iPadAir4Cellular", "iPadEighthGen-iPadEighthGen", "iPadEighthGenCellular-iPadEighthGenCellular", "iPadProThirdGen-iPadProThirdGen", "iPadProThirdGenCellular-iPadProThirdGenCellular", "iPadProFifthGen-iPadProFifthGen", "iPadProFifthGenCellular-iPadProFifthGenCellular", "iPhone13Pro-iPhone13Pro", "iPhone13ProMax-iPhone13ProMax", "iPhone13Mini-iPhone13Mini", "iPhone13-iPhone13", "iPadMiniSixthGen-iPadMiniSixthGen", "iPadMiniSixthGenCellular-iPadMiniSixthGenCellular", "iPadNinthGen-iPadNinthGen", "iPadNinthGenCellular-iPadNinthGenCellular", "iPhoneSEThirdGen-iPhoneSEThirdGen", "iPadAirFifthGen-iPadAirFifthGen", "iPadAirFifthGenCellular-iPadAirFifthGenCellular", "iPhone14-iPhone14", "iPhone14Plus-iPhone14Plus", "iPhone14Pro-iPhone14Pro", "iPhone14ProMax-iPhone14ProMax", "iPadTenthGen-iPadTenthGen", "iPadTenthGenCellular-iPadTenthGenCellular", "iPadPro11FourthGen-iPadPro11FourthGen", "iPadPro11FourthGenCellular-iPadPro11FourthGenCellular", "iPadProSixthGen-iPadProSixthGen", "iPadProSixthGenCellular-iPadProSixthGenCellular", "iPhone15-iPhone15", "iPhone15Plus-iPhone15Plus", "iPhone15Pro-iPhone15Pro", "iPhone15ProMax-iPhone15ProMax", "iPadAir11M2-iPadAir11M2", "iPadAir11M2Cellular-iPadAir11M2Cellular", "iPadAir13M2-iPadAir13M2", "iPadAir13M2Cellular-iPadAir13M2Cellular", "iPadPro11M4-iPadPro11M4", "iPadPro11M4Cellular-iPadPro11M4Cellular", "iPadPro13M4-iPadPro13M4", "iPadPro13M4Cellular-iPadPro13M4Cellular", "iPhone16-iPhone16", "iPhone16Plus-iPhone16Plus", "iPhone16Pro-iPhone16Pro", "iPhone16ProMax-iPhone16ProMax", "iPadMiniA17Pro-iPadMiniA17Pro", "iPadMiniA17ProCellular-iPadMiniA17ProCellular", "iPhone16e-iPhone16e", "iPadA16-iPadA16", "iPadA16Cellular-iPadA16Cellular", "iPadAir11M3-iPadAir11M3", "iPadAir11M3Cellular-iPadAir11M3Cellular", "iPadAir13M3-iPadAir13M3", "iPadAir13M3Cellular-iPadAir13M3Cellular"], "advisories": [], "isGameCenterEnabled": false, "kind": "software", "minimumOsVersion": "16.2", "trackCensoredName": "Hours Tracker: Time Tracking", "trackViewUrl": "https://apps.apple.com/us/app/hours-tracker-time-tracking/id336456412?uo=4", "contentAdvisoryRating": "4+", "averageUserRating": 4.7747, "averageUserRatingForCurrentVersion": 4.7747, "sellerUrl": "http://www.hourstrackerapp.com/?s=appstore", "languageCodesISO2A": ["EN"], "fileSizeBytes": "41762816", "formattedPrice": "Free", "userRatingCountForCurrentVersion": 50702, "trackContentRating": "4+", "artistId": 321923937, "artistName": "Cribasoft, LLC", "genres": ["Business", "Productivity"], "price": 0, "bundleId": "com.carlosribas.HoursTrackerLite", "trackId": 336456412, "trackName": "Hours Tracker: Time Tracking", "releaseNotes": "NEW IN 7.3.4\n\n<PERSON><PERSON>C<PERSON> SWITCH BETWEEN HOURS AND MINUTES PICKERS\nYou can now use the \".\" on the onscreen keyboard to switch between the hours and minutes fields when entering a time entry or break duration.\n\nCOPY FROM LAST ENTRY IMPROVEMENTS\nWe've added the resulting day of the week for the copied entry when using the \"Copy From Last Entry\" feature, making it easier to select the correct date.\n\nNEW IN 7.3.3\n\nIMPROVED SUPPORT FOR 5 MIN MODE\nBy popular request, we've added a setting under More > Preferences that more easily lets you choose to have time pickers operate in 5-min mode. If a job is set to round start/end times, the pickers will still respect and snap to times based on the job's rounding setting. But, for jobs without rounding, this setting switches the time picker between 1 minute intervals and 5 minute intervals.\n\nBUG FIXES\nFixes the Done button sometimes not dismissing the comments screen. Fixes some bugs around the new inline time and duration pickers that crop up when a job is set up with start/end time rounding. It also further improves the usability of the comments editing screen.\n\nNEW IN 7.3:\n\nINLINE EDITING FOR ENTRIES\nThis update is all about making it quicker and easier to add and edit time entries. Now you can select start and end times and durations without navigating to a separate screen. When you edit the duration, whichever time you last set will be preserved. Select a start time and then key in a duration, and we'll calculate the end time automatically. If you prefer to old way of picking time entries, just tap the arrow at the right side of the screen to navigate to that screen.\n\nSEARCH RESPECTS FILTERS\nUsing the search on the Entries and Pay Periods tabs will now take into consideration any active Filters, only finding results that also pass those filters.\n\nUI REFRESH AND BUGFIXES\nWe've streamlined the user interface in a few subtle ways that keep things mostly the same while still making the previous version look old. And of course, we are always fixing bugs and making sure HoursTracker works great on the latest devices and operating systems. We've also improved the experience when editing comments and jumping out and back into the app.", "releaseDate": "2009-11-06T23:42:22Z", "genreIds": ["6000", "6007"], "primaryGenreName": "Business", "primaryGenreId": 6000, "isVppDeviceBasedLicensingEnabled": true, "sellerName": "Cribasoft, LLC", "currentVersionReleaseDate": "2025-03-10T16:15:07Z", "version": "7.3.4", "wrapperType": "software", "currency": "USD", "description": "QUICK AND EASY time entry and editing make time tracking painless\n• Record time using timers, complete with support for Breaks and Pauses, including automatic breaks\n\n• Track your pay, including Tips, Mileage, and flexible ± time and earnings adjustments\n\n• Pick any time to start, stop, break or pause the timer (7 minutes ago, 10 minutes from now, whatever you need)\n\n• Set job locations to get clock in and out reminders when you arrive or leave or fully automate your time tracking (geofencing)\n\n• Manually enter time entries with minimal effort thanks to smart, adaptive defaults\n\n• Enter comments of any length with your time entries and optionally include them in your exports\n\n• Control timers, dictate comments, and apply tags using your Apple Watch\n\nADVANCED FEATURES and customization set HoursTracker above the rest\n• Automatic daily and weekly overtime earnings calculations\n\n• Built-in reports by Day, Week, and Month and support for most common pay period schedules\n\n• Robust tagging and filtering allow you to build your own custom views\n\n• Reminders when you've worked your target number of hours per day (even takes time rounding into account)\n\n• Automatic time rounding: up, down, or to nearest (including 6 min)\n\n• Easily copy an existing job or time entry to save time and effort\n\n• Reminders you to clock in on your selected work days\n\n• Today Widget for at a glance time and pay monitoring\n\n• CSV and formatted text export via e-mail or the iOS Share Sheet\n\n• Passcode lock (with Touch ID & Face ID support) helps keep your HoursTracker data private\n\n• Cloud-based backup/restore with one re-usable backup slot included free (free account sign up is required)\n\n• Web-based reporting access, including charts, graphs, desktop exports, and rolling backups available with optional subscription\n\n• Customize your HoursTracker experience in the Preferences section under the More tab. Choose only one or many jobs clocked in at a time, opt-into prompts for comments, choose an elapsed time format (hours:minutes, or decimal hours), and more\n\n\"Free Edition\" stores up to 3 jobs and 21 days of entries. For unlimited entries, subscribe to \"Premium\" (up to 5 jobs) or \"Unlimited\" (unlimited jobs). Or, erase older data and continue to use the \"Free Edition\" until you're ready to buy.\n\nVisit our website at http://www.hourstrackerapp.com to learn more, and follow @HoursTracker on Twitter or facebook.com/HoursTracker to hear about upcoming features first.\n\nhourstrackerapp.com/privacy\nhourstrackerapp.com/terms", "userRatingCount": 50702}