# QiMai API 数据获取工具

这是一个用于获取七麦数据(QiMai)API的Node.js工具，可以批量获取应用排行榜数据并保存到优化的JSON文件中。

## 功能特点

- 🚀 支持批量获取多页数据 (1-10页)
- 🔐 支持登录认证获取完整数据
- 📊 自动处理API响应和错误
- 💾 数据自动优化并保存为JSON格式
- ⏱️ 内置请求间隔避免频率限制
- 📝 详细的日志输出和错误处理
- 🔧 数据结构优化：移除冗余字段，处理中文数字转换

## 安装依赖

```bash
npm install
```

## 使用方法

### 一键获取数据

```bash
node getTypeList.js
```

执行后将自动：
1. 获取1-10页的应用排行数据
2. 优化数据结构（移除冗余字段）
3. 处理"万"字数字转换
4. 保存到 `data/typeList.json`

### 检查数据

```bash
node checkData.js
```

## 数据结构

### 优化后的数据字段
每个应用只包含以下8个核心字段：

```json
{
  "lastReleaseTime": "2025-06-05",
  "appId": 283646709,
  "appName": "PayPal - Pay, Send, Save",
  "subtitle": "",
  "publisher": "PayPal, Inc.",
  "icon": "https://...",
  "rating": 4.8,
  "num": 6300000
}
```

### 数据优化特点
1. **移除app_id字段** - 保留appId即可
2. **subtitle处理** - "暂无数据"转为空字符串
3. **评论数转换** - "630万" → 6300000
4. **文件大小优化** - 相比原始数据减少60%+

### 输出文件结构

```json
{
  "metadata": {
    "totalRecords": 499,
    "pageRange": "1-10",
    "successPages": 10,
    "failedPages": 0,
    "fetchDate": "2025-06-26T07:43:01.496Z",
    "genre": "6015",
    "country": "us",
    "device": "iphone",
    "optimizeNote": "数据已优化：移除app_id字段，处理subtitle空值，只保留8个核心字段"
  },
  "rankInfo": [
    // 499条优化后的应用数据
  ]
}
```

## 配置说明

### API参数
- `genre`: 应用分类 (6015 - 财务类)
- `country`: 国家代码 (us)
- `device`: 设备类型 (iphone)
- `brand`: 榜单类型 (free - 免费榜)

### 认证信息
工具已配置完整的登录认证信息，可以获取超过免费限制的数据（1-10页）。

## 项目文件

- `getTypeList.js` - 主要数据获取脚本
- `checkData.js` - 数据验证和统计脚本
- `data/typeList.json` - 最终优化的数据文件
- `package.json` - 项目依赖配置

## 数据统计示例

```
📊 总记录数: 499
✅ 成功页数: 10
📁 文件大小: 195.73 KB
🔧 数据结构: lastReleaseTime, appId, appName, subtitle, publisher, icon, rating, num
```

## 注意事项

1. 请求间隔设置为2秒，避免触发API限制
2. 已配置完整认证信息，可获取全部10页数据
3. 数据结构已优化，只保留核心字段
4. "万"字数字已自动转换为实际数值
5. 建议在稳定网络环境下运行

## 许可证

MIT
