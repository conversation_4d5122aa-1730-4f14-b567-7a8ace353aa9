# QiMai API 数据获取工具

这是一个用于获取七麦数据（QiMai）API排行榜信息的Node.js工具。

## 功能特性

- 🚀 批量获取多页排行榜数据
- 💾 自动保存数据到JSON文件
- 🔍 详细的错误处理和日志记录
- 📊 数据统计和分析
- 🔒 自动识别登录限制
- ⏳ 智能请求间隔避免频率限制

## 安装依赖

```bash
npm install axios
```

## 使用方法

### 1. 直接运行获取数据

```bash
node list.js
```

这将获取1-10页的数据（前4页免费，后6页需要登录）并保存到 `data/type.json`

### 2. 只获取免费数据

```javascript
const { fetchFreePages } = require('./list.js');

async function getFreeData() {
    const result = await fetchFreePages('data/free-data.json');
    console.log('获取结果:', result);
}

getFreeData();
```

### 3. 获取指定页面数据

```javascript
const { fetchRankingData } = require('./list.js');

async function getSinglePage() {
    const result = await fetchRankingData(1); // 获取第1页
    console.log('第1页数据:', result);
}

getSinglePage();
```

### 4. 自定义批量获取

```javascript
const { fetchMultiplePages } = require('./list.js');

async function getCustomRange() {
    // 获取第1-3页数据
    const result = await fetchMultiplePages(1, 3, 'data/custom.json');
    console.log('获取结果:', result);
}

getCustomRange();
```

## API 参数说明

当前配置的API参数：
- `brand`: free (免费应用)
- `device`: iphone (iPhone设备)
- `country`: us (美国市场)
- `genre`: 6015 (财务类应用)
- `date`: 2025-06-26 (查询日期)
- `is_rank_index`: 1 (排行榜索引)

## 数据结构

保存的JSON文件包含以下结构：

```json
{
  "metadata": {
    "totalRecords": 200,
    "pageRange": "1-10",
    "successPages": 4,
    "failedPages": 6,
    "fetchDate": "2025-06-26T07:01:06.676Z",
    "genre": "6015",
    "country": "us",
    "device": "iphone"
  },
  "rankInfo": [
    {
      "app_id": 283646709,
      "index": 1,
      "appInfo": {
        "appId": 283646709,
        "appName": "PayPal - Pay, Send, Save",
        "icon": "...",
        "publisher": "PayPal, Inc.",
        "price": "0.00"
      },
      "rank_c": {
        "ranking": 1,
        "change": 0,
        "genre": "财务"
      },
      "comment": {
        "rating": 4.8,
        "num": "630万"
      },
      "pageNumber": 1,
      "fetchTime": "2025-06-26T07:01:06.676Z"
    }
  ]
}
```

## 工具脚本

### 数据统计检查

```bash
node check-data.js
```

显示获取数据的详细统计信息，包括：
- 总记录数和成功/失败页数
- 前10个应用列表
- 各页数据统计
- 评分最高的应用

### 调试单页数据

```bash
node debug-page.js
```

用于调试特定页面的API响应，帮助排查问题。

## 注意事项

1. **登录限制**: 免费用户只能获取前4页数据（每页50条，共200条记录）
2. **请求频率**: 程序自动在请求间添加2秒延迟，避免被限制
3. **数据时效**: 排行榜数据会实时更新，建议定期重新获取
4. **错误处理**: 程序会自动重试失败的请求，并详细记录错误信息

## 文件说明

- `list.js` - 主程序文件，包含所有API请求功能
- `check-data.js` - 数据统计分析工具
- `debug-page.js` - 单页数据调试工具
- `test-summary.js` - API响应摘要测试工具
- `data/type.json` - 保存的排行榜数据文件

## 许可证

MIT License
