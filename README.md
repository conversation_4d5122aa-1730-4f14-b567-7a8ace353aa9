# QiMai API 数据获取工具

这是一个用于获取七麦数据(QiMai)API的Node.js工具，可以批量获取应用排行榜数据并保存到优化的JSON文件中。

## 功能特点

- 🚀 支持批量获取多个应用分类的排行数据
- 🔐 支持登录认证，突破免费用户4页限制
- 📊 自动处理分页请求，可配置页码范围
- 💾 数据自动保存为JSON格式
- ⏱️ 内置请求间隔避免频率限制
- 📝 详细的日志输出和错误处理
- 🔧 数据结构优化：移除冗余字段，处理中文数字转换
- 📱 支持获取应用详细信息（通过iTunes API）
- 🔄 支持断点续传，自动跳过已存在的文件
- 🛡️ 完善的错误处理和重试机制

## 快速开始

### 安装依赖

```bash
npm install
```

### 配置环境变量

复制 `.env` 文件并根据需要修改配置：

```bash
# 页码配置
START_PAGE=1
END_PAGE=10

# API配置
ANALYSIS=your_analysis_token

# 请求头配置
COOKIE_AUTHKEY=your_auth_key
COOKIE_USERINFO=your_user_info
COOKIE_PHPSESSID=your_session_id

# 分类配置 (JSON格式)
GENRE_MAP={"6000":"Business","6001":"Weather",...}
```

## 使用方法

### 一键获取所有分类数据

```bash
node getTypeList.js
```

执行后将自动：
1. 根据 `.env` 配置获取指定页码范围的数据
2. 获取所有配置分类的应用排行数据
3. 为每条数据添加type字段标识分类
4. 优化数据结构（移除冗余字段）
5. 处理"万"字数字转换
6. 合并所有分类数据保存到 `data/typeList.json`

### 获取应用详细信息

```bash
node getAppsInfo.js
```

执行后将自动：
1. 读取 `data/typeList.json` 中的所有 appId
2. 通过 iTunes API 获取每个应用的详细信息
3. 将每个应用信息保存到 `data/appInfo/${appId}.json`
4. 自动跳过已存在的文件，支持断点续传
5. 包含错误处理和重试机制

### 检查排行数据

```bash
node checkData.js
```

### 检查应用详细信息

```bash
node checkAppsInfo.js
```

显示应用详细信息的获取进度和统计：
- 总应用数量和已获取数量
- 成功/失败统计
- 文件大小统计
- 失败应用列表
- 成功获取的应用示例

## 数据结构

### 支持的应用分类

工具支持获取以下24个应用分类的数据：

| 分类ID | 分类名称 | 分类ID | 分类名称 |
|--------|----------|--------|----------|
| 6000 | Business | 6013 | Health & Fitness |
| 6001 | Weather | 6015 | Finance |
| 6002 | Utilities | 6016 | Entertainment |
| 6003 | Travel | 6017 | Education |
| 6004 | Sports | 6018 | Books |
| 6005 | Social Networking | 6020 | Medical |
| 6006 | Reference | 6021 | Magazines & Newspapers |
| 6007 | Productivity | 6023 | Food & Drink |
| 6008 | Photo & Video | 6024 | Shopping |
| 6010 | Navigation | 6026 | Developer Tools |
| 6011 | Music | 6027 | Graphics & Design |
| 6012 | Lifestyle | 6061 | Kids |

### 优化后的数据字段
每个应用包含以下9个核心字段：

```json
{
  "lastReleaseTime": "2025-06-05",
  "appId": *********,
  "appName": "PayPal - Pay, Send, Save",
  "subtitle": "",
  "publisher": "PayPal, Inc.",
  "icon": "https://...",
  "rating": 4.8,
  "num": 6300000,
  "type": "Finance"
}
```

### 数据优化特点
1. **移除app_id字段** - 保留appId即可
2. **添加type字段** - 标识应用分类
3. **subtitle处理** - "暂无数据"转为空字符串
4. **评论数转换** - "630万" → 6300000
5. **多分类合并** - 所有分类数据合并到一个文件

### 输出文件结构

```json
{
  "metadata": {
    "totalRecords": 12000,
    "totalGenres": 24,
    "successPages": 240,
    "failedPages": 0,
    "fetchDate": "2025-06-26T08:00:00.000Z",
    "country": "us",
    "device": "iphone",
    "genres": {
      "6000": "Business",
      "6001": "Weather",
      "6015": "Finance",
      // ... 其他21个分类
    },
    "optimizeNote": "数据已优化：包含所有分类，移除app_id字段，处理subtitle空值，添加type字段"
  },
  "rankInfo": [
    // ~12,000条优化后的应用数据，包含所有分类
  ]
}
```

## 配置说明

### API参数
- `genre`: 应用分类 (6015 - 财务类)
- `country`: 国家代码 (us)
- `device`: 设备类型 (iphone)
- `brand`: 榜单类型 (free - 免费榜)

### 认证信息
工具已配置完整的登录认证信息，可以获取超过免费限制的数据（1-10页）。

## 环境变量配置说明

### 页码配置
- `START_PAGE`: 开始页码（默认：1）
- `END_PAGE`: 结束页码（默认：10）

### API配置
- `ANALYSIS`: API分析参数（必需）

### 请求头配置
- `COOKIE_AUTHKEY`: 认证密钥（必需）
- `COOKIE_USERINFO`: 用户信息（必需）
- `COOKIE_PHPSESSID`: 会话ID（必需）

### 分类配置
- `GENRE_MAP`: JSON格式的分类映射（必需）

## 项目文件

- `getTypeList.js` - 主要的数据获取脚本
- `getAppsInfo.js` - 应用详细信息获取脚本
- `checkData.js` - 排行数据验证和统计脚本
- `checkAppsInfo.js` - 应用详细信息检查脚本
- `.env` - 环境变量配置文件
- `data/typeList.json` - 输出的排行数据文件
- `data/appInfo/` - 应用详细信息目录
- `package.json` - 项目依赖配置

## 数据统计示例

### 数据统计示例
```
📊 总记录数: 根据配置的分类和页码范围
📂 分类数量: 根据GENRE_MAP配置
✅ 成功页数: 根据START_PAGE和END_PAGE配置
📁 文件大小: 取决于数据量
🔧 数据结构: lastReleaseTime, appId, appName, subtitle, publisher, icon, rating, num, type
```

## 应用详细信息功能

### 数据来源
- 使用 iTunes API 获取应用的完整详细信息
- 通过代理服务 `api.allorigins.win` 解决跨域问题
- 每个应用信息保存为独立的JSON文件

### 获取的信息包括
- 应用基本信息（名称、描述、开发商等）
- 应用图标和截图URL
- 版本信息和更新历史
- 评分和评论统计
- 支持的设备和系统要求
- 应用分类和标签
- 价格信息
- 文件大小等技术信息

### 文件结构
```
data/appInfo/
├── 1113153706.json    # Microsoft Teams
├── 309735670.json     # Indeed Job Search
├── 546505307.json     # Zoom Workplace
└── ...                # 其他应用
```

## 注意事项

1. 请求间隔设置为2秒，避免触发API限制
2. 已配置完整认证信息，可获取全部10页数据
3. 数据结构已优化，只保留核心字段
4. "万"字数字已自动转换为实际数值
5. 建议在稳定网络环境下运行

## 许可证

MIT
