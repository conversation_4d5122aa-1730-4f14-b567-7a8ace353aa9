const fs = require('fs');
const path = require('path');

/**
 * 处理评论数字段，将包含"万"的数字转换为实际数值
 * @param {string|number} numStr - 原始评论数
 * @returns {number} - 处理后的数值
 */
function processCommentNum(numStr) {
    if (!numStr) return 0;
    
    // 如果已经是数字，直接返回
    if (typeof numStr === 'number') return numStr;
    
    const str = numStr.toString();
    
    // 检查是否包含"万"字
    if (str.includes('万')) {
        // 提取数字部分，去掉"万"字
        const numberPart = str.replace('万', '').replace(',', '');
        const number = parseFloat(numberPart);
        
        // 如果是有效数字，乘以10000（1万）
        if (!isNaN(number)) {
            return Math.round(number * 10000);
        }
    }
    
    // 如果不包含"万"，尝试解析为数字
    const cleanStr = str.replace(/,/g, ''); // 去掉逗号
    const number = parseFloat(cleanStr);
    return isNaN(number) ? 0 : Math.round(number);
}

/**
 * 简化应用数据，只保留指定字段
 * @param {Object} app - 原始应用数据
 * @returns {Object} - 简化后的应用数据
 */
function simplifyAppData(app) {
    return {
        app_id: app.app_id,
        lastReleaseTime: app.lastReleaseTime,
        appId: app.appInfo?.appId || app.app_id,
        appName: app.appInfo?.appName || '',
        subtitle: app.appInfo?.subtitle || '',
        publisher: app.appInfo?.publisher || '',
        icon: app.appInfo?.icon || '',
        rating: app.comment?.rating || 0,
        num: processCommentNum(app.comment?.num)
    };
}

/**
 * 主简化函数
 */
async function simplifyData() {
    try {
        console.log('🔄 开始简化数据结构...');
        
        // 读取原始数据
        const rawData = fs.readFileSync('./data/type_original_backup.json', 'utf8');
        const data = JSON.parse(rawData);
        
        console.log(`📊 原始数据: ${data.rankInfo.length} 条记录`);
        
        // 简化每条记录
        const simplifiedRankInfo = data.rankInfo.map((app, index) => {
            if (index < 5) {
                // 显示前5条的简化示例
                console.log(`\n📝 简化第${index + 1}条记录:`);
                console.log(`   应用名: ${app.appInfo?.appName}`);
                console.log(`   原始评论数: ${app.comment?.num}`);
                console.log(`   转换后评论数: ${processCommentNum(app.comment?.num)}`);
            }
            
            return simplifyAppData(app);
        });
        
        // 创建简化的数据结构
        const simplifiedData = {
            metadata: {
                totalRecords: simplifiedRankInfo.length,
                pageRange: data.metadata.pageRange,
                successPages: data.metadata.successPages,
                failedPages: data.metadata.failedPages,
                fetchDate: data.metadata.fetchDate,
                genre: data.metadata.genre,
                country: data.metadata.country,
                device: data.metadata.device,
                simplifyDate: new Date().toISOString(),
                simplifyNote: '数据已简化：只保留核心字段(app_id, lastReleaseTime, appId, appName, subtitle, publisher, icon, rating, num)'
            },
            rankInfo: simplifiedRankInfo
        };
        
        // 保存简化后的数据
        const outputPath = './data/type_simplified.json';
        fs.writeFileSync(outputPath, JSON.stringify(simplifiedData, null, 2), 'utf8');
        
        console.log('\n✅ 数据简化完成!');
        console.log(`📁 简化后文件: ${path.resolve(outputPath)}`);
        console.log(`📊 简化后记录数: ${simplifiedData.rankInfo.length}`);
        
        // 计算文件大小对比
        const originalSize = fs.statSync('./data/type_original_backup.json').size;
        const simplifiedSize = fs.statSync(outputPath).size;
        const reduction = ((originalSize - simplifiedSize) / originalSize * 100).toFixed(1);
        
        console.log('\n📈 文件大小对比:');
        console.log(`   原始文件: ${(originalSize / 1024).toFixed(2)} KB`);
        console.log(`   简化文件: ${(simplifiedSize / 1024).toFixed(2)} KB`);
        console.log(`   减少: ${reduction}%`);
        
        // 显示简化统计
        const stats = {
            totalRecords: simplifiedData.rankInfo.length,
            withWanChar: 0,
            convertedNums: 0,
            avgRating: 0
        };
        
        let totalRating = 0;
        simplifiedData.rankInfo.forEach(app => {
            if (data.rankInfo.find(orig => orig.app_id === app.app_id)?.comment?.num?.toString().includes('万')) {
                stats.withWanChar++;
            }
            if (app.num > 10000) {
                stats.convertedNums++;
            }
            totalRating += app.rating;
        });
        
        stats.avgRating = (totalRating / stats.totalRecords).toFixed(2);
        
        console.log('\n📈 简化统计:');
        console.log(`   包含"万"字的记录: ${stats.withWanChar} 条`);
        console.log(`   转换后评论数>1万的记录: ${stats.convertedNums} 条`);
        console.log(`   平均评分: ${stats.avgRating} 分`);
        
        // 显示几个简化示例
        console.log('\n🔍 简化示例 (前3条):');
        simplifiedData.rankInfo.slice(0, 3).forEach((app, i) => {
            console.log(`${i + 1}. ${app.appName}`);
            console.log(`   app_id: ${app.app_id}`);
            console.log(`   appId: ${app.appId}`);
            console.log(`   publisher: ${app.publisher}`);
            console.log(`   rating: ${app.rating}`);
            console.log(`   num: ${app.num.toLocaleString()}`);
            console.log(`   lastReleaseTime: ${app.lastReleaseTime}`);
        });
        
        // 验证字段完整性
        console.log('\n🔧 字段验证:');
        const requiredFields = ['app_id', 'lastReleaseTime', 'appId', 'appName', 'subtitle', 'publisher', 'icon', 'rating', 'num'];
        const firstApp = simplifiedData.rankInfo[0];
        
        requiredFields.forEach(field => {
            const exists = firstApp.hasOwnProperty(field);
            console.log(`   ${exists ? '✅' : '❌'} ${field}: ${exists ? '存在' : '缺失'}`);
        });
        
        return simplifiedData;
        
    } catch (error) {
        console.error('❌ 简化过程中发生错误:', error.message);
        throw error;
    }
}

// 如果直接运行此文件，则执行简化
if (require.main === module) {
    simplifyData()
        .then(() => {
            console.log('\n🎉 数据简化任务完成!');
            console.log('💡 提示: 可以使用简化后的 data/type_simplified.json 文件');
            console.log('🔄 如需替换主文件，请运行: copy data\\type_simplified.json data\\type.json');
        })
        .catch(error => {
            console.error('💥 简化失败:', error.message);
            process.exit(1);
        });
}

module.exports = {
    simplifyData,
    simplifyAppData,
    processCommentNum
};
