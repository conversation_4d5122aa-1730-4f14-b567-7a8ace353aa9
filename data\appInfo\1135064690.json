{"isGameCenterEnabled": false, "artworkUrl60": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/5e/bc/b4/5ebcb4fe-678c-511a-e474-a161ebfa6c71/SCIcon-0-1x_U007emarketing-0-7-0-85-220-0.png/60x60bb.jpg", "artworkUrl100": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/5e/bc/b4/5ebcb4fe-678c-511a-e474-a161ebfa6c71/SCIcon-0-1x_U007emarketing-0-7-0-85-220-0.png/100x100bb.jpg", "artistViewUrl": "https://apps.apple.com/us/developer/cisco/id298844389?uo=4", "supportedDevices": ["iPhone5s-iPhone5s", "iPadAir-iPadAir", "iPadAirCellular-iPadAirCellular", "iPadMiniRetina-iPadMiniRetina", "iPadMiniRetinaCellular-iPadMiniRetinaCellular", "iPhone6-iPhone6", "iPhone6Plus-iPhone6Plus", "iPadAir2-iPadAir2", "iPadAir2Cellular-iPadAir2Cellular", "iPadMini3-iPadMini3", "iPadMini3Cellular-iPadMini3Cellular", "iPodTouchSixthGen-iPodTouchSixthGen", "iPhone6s-iPhone6s", "iPhone6sPlus-iPhone6sPlus", "iPadMini4-iPadMini4", "iPadMini4Cellular-iPadMini4Cellular", "iPadPro-iPadPro", "iPadProCellular-iPadProCellular", "iPadPro97-iPadPro97", "iPadPro97Cellular-iPadPro97Cellular", "iPhoneSE-iPhoneSE", "iPhone7-iPhone7", "iPhone7Plus-iPhone7Plus", "iPad611-iPad611", "iPad612-iPad612", "iPad71-iPad71", "iPad72-iPad72", "iPad73-iPad73", "iPad74-iPad74", "iPhone8-iPhone8", "iPhone8Plus-iPhone8Plus", "iPhoneX-iPhoneX", "iPad75-iPad75", "iPad76-iPad76", "iPhoneXS-iPhoneXS", "iPhoneXSMax-iPhoneXSMax", "iPhoneXR-iPhoneXR", "iPad812-iPad812", "iPad834-iPad834", "iPad856-iPad856", "iPad878-iPad878", "iPadMini5-iPadMini5", "iPadMini5Cellular-iPadMini5Cellular", "iPadAir3-iPadAir3", "iPadAir3Cellular-iPadAir3Cellular", "iPodTouchSeventhGen-iPodTouchSeventhGen", "iPhone11-iPhone11", "iPhone11Pro-iPhone11Pro", "iPadSeventhGen-iPadSeventhGen", "iPadSeventhGenCellular-iPadSeventhGenCellular", "iPhone11ProMax-iPhone11ProMax", "iPhoneSESecondGen-iPhoneSESecondGen", "iPadProSecondGen-iPadProSecondGen", "iPadProSecondGenCellular-iPadProSecondGenCellular", "iPadProFourthGen-iPadProFourthGen", "iPadProFourthGenCellular-iPadProFourthGenCellular", "iPhone12Mini-iPhone12Mini", "iPhone12-iPhone12", "iPhone12Pro-iPhone12Pro", "iPhone12ProMax-iPhone12ProMax", "iPadAir4-iPadAir4", "iPadAir4Cellular-iPadAir4Cellular", "iPadEighthGen-iPadEighthGen", "iPadEighthGenCellular-iPadEighthGenCellular", "iPadProThirdGen-iPadProThirdGen", "iPadProThirdGenCellular-iPadProThirdGenCellular", "iPadProFifthGen-iPadProFifthGen", "iPadProFifthGenCellular-iPadProFifthGenCellular", "iPhone13Pro-iPhone13Pro", "iPhone13ProMax-iPhone13ProMax", "iPhone13Mini-iPhone13Mini", "iPhone13-iPhone13", "iPadMiniSixthGen-iPadMiniSixthGen", "iPadMiniSixthGenCellular-iPadMiniSixthGenCellular", "iPadNinthGen-iPadNinthGen", "iPadNinthGenCellular-iPadNinthGenCellular", "iPhoneSEThirdGen-iPhoneSEThirdGen", "iPadAirFifthGen-iPadAirFifthGen", "iPadAirFifthGenCellular-iPadAirFifthGenCellular", "iPhone14-iPhone14", "iPhone14Plus-iPhone14Plus", "iPhone14Pro-iPhone14Pro", "iPhone14ProMax-iPhone14ProMax", "iPadTenthGen-iPadTenthGen", "iPadTenthGenCellular-iPadTenthGenCellular", "iPadPro11FourthGen-iPadPro11FourthGen", "iPadPro11FourthGenCellular-iPadPro11FourthGenCellular", "iPadProSixthGen-iPadProSixthGen", "iPadProSixthGenCellular-iPadProSixthGenCellular", "iPhone15-iPhone15", "iPhone15Plus-iPhone15Plus", "iPhone15Pro-iPhone15Pro", "iPhone15ProMax-iPhone15ProMax", "iPadAir11M2-iPadAir11M2", "iPadAir11M2Cellular-iPadAir11M2Cellular", "iPadAir13M2-iPadAir13M2", "iPadAir13M2Cellular-iPadAir13M2Cellular", "iPadPro11M4-iPadPro11M4", "iPadPro11M4Cellular-iPadPro11M4Cellular", "iPadPro13M4-iPadPro13M4", "iPadPro13M4Cellular-iPadPro13M4Cellular", "iPhone16-iPhone16", "iPhone16Plus-iPhone16Plus", "iPhone16Pro-iPhone16Pro", "iPhone16ProMax-iPhone16ProMax", "iPadMiniA17Pro-iPadMiniA17Pro", "iPadMiniA17ProCellular-iPadMiniA17ProCellular", "iPhone16e-iPhone16e", "iPadA16-iPadA16", "iPadA16Cellular-iPadA16Cellular", "iPadAir11M3-iPadAir11M3", "iPadAir11M3Cellular-iPadAir11M3Cellular", "iPadAir13M3-iPadAir13M3", "iPadAir13M3Cellular-iPadAir13M3Cellular"], "features": ["iosUniversal"], "advisories": [], "kind": "software", "screenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/PurpleSource112/v4/69/dd/e6/69dde66e-3bd3-7afb-402d-888d601db2bc/154e4f08-45cc-45f3-afae-46b303abe337_5.5-portrait.png/392x696bb.png"], "ipadScreenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/PurpleSource122/v4/ab/6f/18/ab6f18a8-3385-56da-a35b-f0dfc1be2963/ed646a20-e512-497f-b2fe-b5b7c14c0010_12.9-2nd-portrait.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource112/v4/79/fa/c0/79fac07d-2e84-fae0-bb18-ade18bbb48c8/2025a1e7-09f7-4bb6-b276-f4f00db74765_12.9-2nd-landscape.png/552x414bb.png"], "appletvScreenshotUrls": [], "artworkUrl512": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/5e/bc/b4/5ebcb4fe-678c-511a-e474-a161ebfa6c71/SCIcon-0-1x_U007emarketing-0-7-0-85-220-0.png/512x512bb.jpg", "contentAdvisoryRating": "4+", "trackCensoredName": "Cisco Secure Client", "trackViewUrl": "https://apps.apple.com/us/app/cisco-secure-client/id1135064690?uo=4", "averageUserRating": 3.93228, "artistId": 298844389, "artistName": "Cisco", "genres": ["Business", "Utilities"], "price": 0, "bundleId": "com.cisco.anyconnect", "trackId": 1135064690, "trackName": "Cisco Secure Client", "genreIds": ["6000", "6002"], "primaryGenreName": "Business", "primaryGenreId": 6000, "isVppDeviceBasedLicensingEnabled": true, "sellerName": "Cisco", "currentVersionReleaseDate": "2024-10-24T14:07:00Z", "releaseNotes": "General improvements and bug fixes.\nPlease report any questions or <NAME_EMAIL>.", "version": "5.1.6107", "wrapperType": "software", "currency": "USD", "description": "This is the Cisco Secure Client (including AnyConnect VPN) application for Apple iOS. \n\nPlease report any <NAME_EMAIL>.\n\nPlease consult with your EMM/MDM vendor on configuration changes required to configure this new version if you are not setting it up manually.  Samples at: https://community.cisco.com/t5/security-blogs/anyconnect-apple-ios-transition-to-apple-s-latest-vpn-framework/ba-p/3098264 \n\nLICENSING AND INFRASTRUCTURE REQUIREMENTS:\n\nYou must have an active AnyConnect Plus, Apex or VPN Only term/contract to utilize this software. Use is no longer permitted for older Essentials/Premium with Mobile licensing.  AnyConnect may never be used with non-Cisco servers.\n\nTrial AnyConnect Apex (ASA) licenses are available for administrators at www.cisco.com/go/license\n\nAnyConnect for iOS requires Cisco Adaptive Security Appliance (ASA) Boot image 8.0(4) or later. \n\nPer App VPN requires ASA 9.3(2) or later (5500-X/ASAv only) with Plus, Apex or VPN Only licensing and a minimum Apple iOS version of 10.x.\n\nFor additional licensing questions, please contact ac-mobile-license-request (AT) cisco.com and include a copy of \"show version\" from your Cisco ASA.\n\nOrdering and Licensing Guide:\nhttps://www.cisco.com/c/en/us/products/collateral/security/anyconnect-secure-mobility-client/secure-client-og.html\n\nCisco Secure Client (including AnyConnect VPN) provides reliable and easy-to-deploy encrypted network connectivity from any Apple iOS by delivering persistent corporate access for users on the go. Whether providing access to business email, a virtual desktop session, or most other iOS applications, AnyConnect enables business-critical application connectivity. Through the use of Datagram Transport Layer Security (DTLS), TCP-based applications and latency-sensitive traffic (such as voice over IP [VoIP]) are provided an optimized communication path to corporate resources.\nAdditionally, the Cisco Secure Client support IPsec IKEv2 with Next Generation Encryption.\n \nFeatures:\n \n- Automatically adapts its tunneling to the most efficient method possible based on network constraints, using TLS and DTLS.\n- DTLS provides an optimized connection for TCP-based application access and latency-sensitive traffic, such as VoIP traffic\n- Network roaming capability allows connectivity to resume seamlessly after IP address change, loss of connectivity, or device standby\n- Wide Range of Authentication Options: RADIUS, RSA SecurID, Active Directory/Kerberos, Digital Certificates, LDAP, multifactor authentication\n- Supports certificate deployment using Apple iOS and AnyConnect integrated SCEP\n- Compatible with Apple iOS Connect On Demand VPN capability for automatic VPN connections when required by an application\n- Policies can be preconfigured or configured locally, and can be automatically updated from the VPN headend\n- Access to internal IPv4 and IPv6 network resources\n- Administrator-controlled split / full tunneling network access policy\n- Per App VPN (TCP  and UDP) - MDM controlled\n\nIf you are an end-user and have any issues or concerns, please contact your organization’s support department. If you are a System Administrator having difficulties configuring or utilizing the Application, please contact your designated support point of contact. \n\nRelease Notes: \nhttps://www.cisco.com/c/en/us/support/security/anyconnect-secure-mobility-client/products-release-notes-list.html\n\nUser Guide:\nhttps://www.cisco.com/c/en/us/support/security/anyconnect-secure-mobility-client/products-user-guide-list.html\n\nEnd user license:\nhttps://www.cisco.com/c/dam/en_us/about/doing_business/legal/seula/anyconnect-SEULA-v4x.pdf", "userRatingCountForCurrentVersion": 2156, "averageUserRatingForCurrentVersion": 3.93228, "sellerUrl": "http://www.cisco.com/go/anyconnect", "languageCodesISO2A": ["EN"], "fileSizeBytes": "26011648", "formattedPrice": "Free", "trackContentRating": "4+", "minimumOsVersion": "13.0", "releaseDate": "2017-06-14T12:45:48Z", "userRatingCount": 2156}