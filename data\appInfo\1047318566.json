{"screenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/Purple122/v4/b3/d6/3e/b3d63e36-bc3d-9ee4-8c89-4574e4aeae83/38b5353a-3eb3-4b69-8747-2c2a51a8b6f8_01a.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple122/v4/6c/09/c9/6c09c9ca-cbb7-bb76-1559-7ac4fd1137cb/85d73c39-9ae0-4a18-b049-d427fd2e6104_01b.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple122/v4/66/d3/3c/66d33cb2-d469-6d24-627a-ca18cbbf4e80/256fe6d4-68cb-4be7-a01e-4dcea4ab201f_02.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple112/v4/87/d5/d3/87d5d3f7-edc6-b83d-1c06-5d9b2e6ad0f6/70d9319f-3e13-48b3-ae71-b1affb7ae391_03.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple112/v4/2b/a7/a9/2ba7a9d7-3d41-146a-7472-c0566da21496/407dcd2d-69d5-4232-8229-92a18e5e88bc_04.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple112/v4/01/ef/c9/01efc98b-4f85-f5a2-8419-c6a08d04c40f/2d5a2d46-c3d5-42b2-adc6-127fdb528aed_05.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple112/v4/97/59/d8/9759d852-501f-1a07-dc57-74347e89d3c6/d5216c30-eb38-45c5-9258-5c7cef830586_06.png/392x696bb.png"], "ipadScreenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/Purple112/v4/c6/29/ae/c629ae6f-55d0-69ab-0a8b-476e641cc9a0/66a497a2-9a91-4a1e-8cee-2891c0ad462e_01.png/552x414bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple112/v4/46/0a/9e/460a9eb5-9081-10fa-c5f8-ba6089ed389d/2ea30d9d-b099-45ea-b14a-23f0f3ebab9c_02.png/552x414bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple122/v4/5a/6d/fe/5a6dfe23-652e-a15a-df58-1fb5d1b204c1/594ae289-c9b3-45e8-839f-43a2acf63a5f_03.png/552x414bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple122/v4/c8/14/bc/c814bc9a-40dc-f342-35a5-fa3d3ecc837a/fc315e74-f5e5-4ca4-b713-ecd70317c806_04.png/552x414bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple122/v4/16/a0/8e/16a08e8b-51b2-45db-c3b3-2e27763b94be/918f929c-d149-4720-b346-a7c81c1ed4a3_05.png/552x414bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple122/v4/c6/34/a1/c634a1c7-0836-1c91-9b60-99342c37d7d1/25c55446-6e9f-4c50-b44c-c517ee5a5614_06.png/552x414bb.png"], "appletvScreenshotUrls": [], "artworkUrl512": "https://is1-ssl.mzstatic.com/image/thumb/Purple221/v4/f0/17/6e/f0176e30-1f8f-7bcc-f413-9d70ef94357a/PowerApps_AppIcon-1x_U007emarketing-0-8-0-85-220-0.png/512x512bb.jpg", "features": ["iosUniversal"], "supportedDevices": ["iPhone5s-iPhone5s", "iPadAir-iPadAir", "iPadAirCellular-iPadAirCellular", "iPadMiniRetina-iPadMiniRetina", "iPadMiniRetinaCellular-iPadMiniRetinaCellular", "iPhone6-iPhone6", "iPhone6Plus-iPhone6Plus", "iPadAir2-iPadAir2", "iPadAir2Cellular-iPadAir2Cellular", "iPadMini3-iPadMini3", "iPadMini3Cellular-iPadMini3Cellular", "iPodTouchSixthGen-iPodTouchSixthGen", "iPhone6s-iPhone6s", "iPhone6sPlus-iPhone6sPlus", "iPadMini4-iPadMini4", "iPadMini4Cellular-iPadMini4Cellular", "iPadPro-iPadPro", "iPadProCellular-iPadProCellular", "iPadPro97-iPadPro97", "iPadPro97Cellular-iPadPro97Cellular", "iPhoneSE-iPhoneSE", "iPhone7-iPhone7", "iPhone7Plus-iPhone7Plus", "iPad611-iPad611", "iPad612-iPad612", "iPad71-iPad71", "iPad72-iPad72", "iPad73-iPad73", "iPad74-iPad74", "iPhone8-iPhone8", "iPhone8Plus-iPhone8Plus", "iPhoneX-iPhoneX", "iPad75-iPad75", "iPad76-iPad76", "iPhoneXS-iPhoneXS", "iPhoneXSMax-iPhoneXSMax", "iPhoneXR-iPhoneXR", "iPad812-iPad812", "iPad834-iPad834", "iPad856-iPad856", "iPad878-iPad878", "iPadMini5-iPadMini5", "iPadMini5Cellular-iPadMini5Cellular", "iPadAir3-iPadAir3", "iPadAir3Cellular-iPadAir3Cellular", "iPodTouchSeventhGen-iPodTouchSeventhGen", "iPhone11-iPhone11", "iPhone11Pro-iPhone11Pro", "iPadSeventhGen-iPadSeventhGen", "iPadSeventhGenCellular-iPadSeventhGenCellular", "iPhone11ProMax-iPhone11ProMax", "iPhoneSESecondGen-iPhoneSESecondGen", "iPadProSecondGen-iPadProSecondGen", "iPadProSecondGenCellular-iPadProSecondGenCellular", "iPadProFourthGen-iPadProFourthGen", "iPadProFourthGenCellular-iPadProFourthGenCellular", "iPhone12Mini-iPhone12Mini", "iPhone12-iPhone12", "iPhone12Pro-iPhone12Pro", "iPhone12ProMax-iPhone12ProMax", "iPadAir4-iPadAir4", "iPadAir4Cellular-iPadAir4Cellular", "iPadEighthGen-iPadEighthGen", "iPadEighthGenCellular-iPadEighthGenCellular", "iPadProThirdGen-iPadProThirdGen", "iPadProThirdGenCellular-iPadProThirdGenCellular", "iPadProFifthGen-iPadProFifthGen", "iPadProFifthGenCellular-iPadProFifthGenCellular", "iPhone13Pro-iPhone13Pro", "iPhone13ProMax-iPhone13ProMax", "iPhone13Mini-iPhone13Mini", "iPhone13-iPhone13", "iPadMiniSixthGen-iPadMiniSixthGen", "iPadMiniSixthGenCellular-iPadMiniSixthGenCellular", "iPadNinthGen-iPadNinthGen", "iPadNinthGenCellular-iPadNinthGenCellular", "iPhoneSEThirdGen-iPhoneSEThirdGen", "iPadAirFifthGen-iPadAirFifthGen", "iPadAirFifthGenCellular-iPadAirFifthGenCellular", "iPhone14-iPhone14", "iPhone14Plus-iPhone14Plus", "iPhone14Pro-iPhone14Pro", "iPhone14ProMax-iPhone14ProMax", "iPadTenthGen-iPadTenthGen", "iPadTenthGenCellular-iPadTenthGenCellular", "iPadPro11FourthGen-iPadPro11FourthGen", "iPadPro11FourthGenCellular-iPadPro11FourthGenCellular", "iPadProSixthGen-iPadProSixthGen", "iPadProSixthGenCellular-iPadProSixthGenCellular", "iPhone15-iPhone15", "iPhone15Plus-iPhone15Plus", "iPhone15Pro-iPhone15Pro", "iPhone15ProMax-iPhone15ProMax", "iPadAir11M2-iPadAir11M2", "iPadAir11M2Cellular-iPadAir11M2Cellular", "iPadAir13M2-iPadAir13M2", "iPadAir13M2Cellular-iPadAir13M2Cellular", "iPadPro11M4-iPadPro11M4", "iPadPro11M4Cellular-iPadPro11M4Cellular", "iPadPro13M4-iPadPro13M4", "iPadPro13M4Cellular-iPadPro13M4Cellular", "iPhone16-iPhone16", "iPhone16Plus-iPhone16Plus", "iPhone16Pro-iPhone16Pro", "iPhone16ProMax-iPhone16ProMax", "iPadMiniA17Pro-iPadMiniA17Pro", "iPadMiniA17ProCellular-iPadMiniA17ProCellular", "iPhone16e-iPhone16e", "iPadA16-iPadA16", "iPadA16Cellular-iPadA16Cellular", "iPadAir11M3-iPadAir11M3", "iPadAir11M3Cellular-iPadAir11M3Cellular", "iPadAir13M3-iPadAir13M3", "iPadAir13M3Cellular-iPadAir13M3Cellular"], "isGameCenterEnabled": false, "advisories": [], "kind": "software", "artistViewUrl": "https://apps.apple.com/us/developer/microsoft-corporation/id298856275?uo=4", "artworkUrl60": "https://is1-ssl.mzstatic.com/image/thumb/Purple221/v4/f0/17/6e/f0176e30-1f8f-7bcc-f413-9d70ef94357a/PowerApps_AppIcon-1x_U007emarketing-0-8-0-85-220-0.png/60x60bb.jpg", "artworkUrl100": "https://is1-ssl.mzstatic.com/image/thumb/Purple221/v4/f0/17/6e/f0176e30-1f8f-7bcc-f413-9d70ef94357a/PowerApps_AppIcon-1x_U007emarketing-0-8-0-85-220-0.png/100x100bb.jpg", "averageUserRatingForCurrentVersion": 4.85297, "minimumOsVersion": "16.0", "fileSizeBytes": "132320256", "formattedPrice": "Free", "userRatingCountForCurrentVersion": 22445, "trackContentRating": "4+", "sellerUrl": "https://powerapps.microsoft.com", "languageCodesISO2A": ["EU", "BG", "CA", "HR", "CS", "DA", "NL", "EN", "ET", "FI", "FR", "GL", "DE", "EL", "HI", "HU", "ID", "IT", "JA", "KK", "KO", "LV", "LT", "NB", "PL", "PT", "RO", "RU", "SR", "ZH", "SK", "SL", "ES", "SV", "TH", "ZH", "TR", "UK"], "artistId": 298856275, "artistName": "Microsoft Corporation", "genres": ["Business", "Utilities"], "price": 0, "bundleId": "com.microsoft.msapps", "trackId": 1047318566, "trackName": "Power Apps", "releaseDate": "2015-11-30T15:04:47Z", "genreIds": ["6000", "6002"], "releaseNotes": "• Bug fixes and performance improvements", "primaryGenreName": "Business", "primaryGenreId": 6000, "isVppDeviceBasedLicensingEnabled": true, "sellerName": "Microsoft Corporation", "currentVersionReleaseDate": "2025-06-24T09:06:34Z", "version": "3.25063.5", "wrapperType": "software", "currency": "USD", "description": "Get Power Apps to conveniently access your work or school apps no matter where you are: at home, on the road, in the field, off-campus, at the airport, or at the beach – anywhere life takes you.\n\nWHAT’S INSIDE\n\nThe Power Apps app is the front door to the apps at your work or school. Which apps can you use? It depends on what’s been created for you. Here are some examples you might see, or ones you can make yourself using the Power Apps website:\n\n• Campus app: Map your campus with icons for landmarks and facility details.\n• Event registration app: Record attendees as they arrive using barcodes or QR codes.\n• Expenses app: Let employees submit their expenses and upload photos of receipts.\n• Health clinic app: Let patients check in to appointments with just a few taps.\n• NFC reader app: Scan NFC tags on ID cards, equipment, packages, etc.\n• Performance app: Visualize data and get insights with interactive dashboards.\n• Sales app: See opportunities and leads, review comments, and approve for your P&L.\n• Space planning app: Take 3D measurements and manipulate objects in mixed reality.\n• Timesheet app: Collect, consolidate, and analyze shift data from employees.\n\nThis is just a handful of examples; the possibilities are endless. Build and share low-code apps for your work or school at the Power Apps website.\n\nTIPS\n\n• Swipe right to make an app a favorite, swipe left to add a shortcut to the home screen.\n• As an admin, mark an app as Featured, so that it stays pinned to the top of the apps list.\n• Some apps can work offline, and Power Apps will sync your data when you reconnect.\n\nAccessibility: https://go.microsoft.com/fwlink/?linkid=2121429", "trackCensoredName": "Power Apps", "trackViewUrl": "https://apps.apple.com/us/app/power-apps/id1047318566?uo=4", "contentAdvisoryRating": "4+", "averageUserRating": 4.85297, "userRatingCount": 22445}