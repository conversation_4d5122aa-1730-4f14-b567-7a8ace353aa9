const fs = require('fs');

// 读取简化后的数据
const data = JSON.parse(fs.readFileSync('./data/type.json', 'utf8'));

console.log('🔍 简化数据验证报告');
console.log('='.repeat(60));

// 基本信息验证
console.log('\n📊 基本信息:');
console.log(`✅ 总记录数: ${data.rankInfo.length}`);
console.log(`📅 简化时间: ${new Date(data.metadata.simplifyDate).toLocaleString('zh-CN')}`);

// 验证只包含指定字段
console.log('\n🔧 字段结构验证:');
const requiredFields = [
    'app_id', 'lastReleaseTime', 'appId', 'appName', 
    'subtitle', 'publisher', 'icon', 'rating', 'num'
];

const firstApp = data.rankInfo[0];
const actualFields = Object.keys(firstApp);

console.log('   要求的字段:');
requiredFields.forEach(field => {
    const exists = firstApp.hasOwnProperty(field);
    console.log(`   ${exists ? '✅' : '❌'} ${field}: ${exists ? '存在' : '缺失'}`);
});

// 检查是否有多余字段
const extraFields = actualFields.filter(field => !requiredFields.includes(field));
if (extraFields.length > 0) {
    console.log('\n   ⚠️  发现多余字段:');
    extraFields.forEach(field => {
        console.log(`   ❌ ${field}: 应该移除`);
    });
} else {
    console.log('\n   ✅ 没有多余字段，数据结构完全符合要求');
}

// 数据类型验证
console.log('\n🔢 数据类型验证:');
const typeChecks = {
    app_id: 'number',
    lastReleaseTime: 'string',
    appId: 'number',
    appName: 'string',
    subtitle: 'string',
    publisher: 'string',
    icon: 'string',
    rating: 'number',
    num: 'number'
};

let typeErrors = 0;
Object.entries(typeChecks).forEach(([field, expectedType]) => {
    const actualType = typeof firstApp[field];
    const isCorrect = actualType === expectedType;
    if (!isCorrect) typeErrors++;
    console.log(`   ${isCorrect ? '✅' : '❌'} ${field}: ${actualType} (期望: ${expectedType})`);
});

// 数据完整性检查
console.log('\n📋 数据完整性检查:');
const completeness = {
    hasAppName: data.rankInfo.filter(app => app.appName && app.appName !== '').length,
    hasPublisher: data.rankInfo.filter(app => app.publisher && app.publisher !== '').length,
    hasIcon: data.rankInfo.filter(app => app.icon && app.icon !== '').length,
    hasRating: data.rankInfo.filter(app => app.rating > 0).length,
    hasComments: data.rankInfo.filter(app => app.num > 0).length,
    hasReleaseTime: data.rankInfo.filter(app => app.lastReleaseTime && app.lastReleaseTime !== '').length
};

Object.entries(completeness).forEach(([field, count]) => {
    const percentage = ((count / data.rankInfo.length) * 100).toFixed(1);
    console.log(`   ${field}: ${count}/${data.rankInfo.length} (${percentage}%)`);
});

// 评论数转换验证
console.log('\n💯 评论数转换验证:');
const numStats = {
    over1M: data.rankInfo.filter(app => app.num >= 1000000).length,
    over100K: data.rankInfo.filter(app => app.num >= 100000).length,
    over10K: data.rankInfo.filter(app => app.num >= 10000).length,
    under10K: data.rankInfo.filter(app => app.num < 10000).length
};

console.log(`   100万+评论: ${numStats.over1M} 个应用`);
console.log(`   10万+评论: ${numStats.over100K} 个应用`);
console.log(`   1万+评论: ${numStats.over10K} 个应用`);
console.log(`   1万以下评论: ${numStats.under10K} 个应用`);

// 显示数据示例
console.log('\n📝 数据示例 (前3条):');
data.rankInfo.slice(0, 3).forEach((app, i) => {
    console.log(`\n${i + 1}. ${app.appName}`);
    console.log(`   app_id: ${app.app_id}`);
    console.log(`   lastReleaseTime: ${app.lastReleaseTime}`);
    console.log(`   appId: ${app.appId}`);
    console.log(`   subtitle: ${app.subtitle}`);
    console.log(`   publisher: ${app.publisher}`);
    console.log(`   rating: ${app.rating}`);
    console.log(`   num: ${app.num.toLocaleString()}`);
    console.log(`   icon: ${app.icon.substring(0, 50)}...`);
});

// 文件大小信息
const fileSize = fs.statSync('./data/type.json').size;
console.log('\n📁 文件信息:');
console.log(`   文件大小: ${(fileSize / 1024).toFixed(2)} KB`);
console.log(`   平均每条记录: ${(fileSize / data.rankInfo.length).toFixed(0)} 字节`);

// 最受欢迎的应用
console.log('\n🏆 最受欢迎的应用 (按评论数):');
const topByComments = data.rankInfo
    .sort((a, b) => b.num - a.num)
    .slice(0, 5);

topByComments.forEach((app, i) => {
    console.log(`   ${i + 1}. ${app.appName}`);
    console.log(`      发布商: ${app.publisher}`);
    console.log(`      评论数: ${app.num.toLocaleString()}`);
    console.log(`      评分: ${app.rating}`);
});

// 验证总结
console.log('\n' + '='.repeat(60));
if (typeErrors === 0 && extraFields.length === 0) {
    console.log('✅ 数据简化验证通过！');
    console.log('🎉 数据结构完全符合要求，只包含指定的9个字段');
    console.log('💯 评论数"万"字转换正确');
} else {
    console.log(`❌ 发现问题:`);
    if (typeErrors > 0) console.log(`   - ${typeErrors} 个类型错误`);
    if (extraFields.length > 0) console.log(`   - ${extraFields.length} 个多余字段`);
}
console.log('📁 简化后的数据文件: data/type.json');
console.log('='.repeat(60));
