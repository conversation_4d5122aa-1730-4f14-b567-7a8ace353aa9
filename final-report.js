const data = require('./data/type.json');

console.log('🎉 QiMai API 数据获取完成报告');
console.log('='.repeat(80));

// 基本统计
console.log('\n📊 基本统计信息:');
console.log(`✅ 总记录数: ${data.metadata.totalRecords} 条`);
console.log(`📄 成功页数: ${data.metadata.successPages} 页`);
console.log(`❌ 失败页数: ${data.metadata.failedPages} 页`);
console.log(`📅 获取时间: ${new Date(data.metadata.fetchDate).toLocaleString('zh-CN')}`);
console.log(`🏷️  应用类型: 财务类应用 (genre: ${data.metadata.genre})`);
console.log(`🌍 目标市场: ${data.metadata.country.toUpperCase()}`);
console.log(`📱 设备平台: ${data.metadata.device}`);

// 文件信息
const fs = require('fs');
const fileStats = fs.statSync('./data/type.json');
console.log(`📁 文件大小: ${(fileStats.size / 1024).toFixed(2)} KB`);

// 排名分布
console.log('\n🏆 排名分布统计:');
const rankingRanges = {
    'Top 50 (1-50)': 0,
    'Top 100 (51-100)': 0,
    'Top 200 (101-200)': 0,
    'Top 300 (201-300)': 0,
    'Top 400 (301-400)': 0,
    'Top 500 (401-500)': 0
};

data.rankInfo.forEach(app => {
    const ranking = app.rank_c.ranking;
    if (ranking <= 50) rankingRanges['Top 50 (1-50)']++;
    else if (ranking <= 100) rankingRanges['Top 100 (51-100)']++;
    else if (ranking <= 200) rankingRanges['Top 200 (101-200)']++;
    else if (ranking <= 300) rankingRanges['Top 300 (201-300)']++;
    else if (ranking <= 400) rankingRanges['Top 400 (301-400)']++;
    else if (ranking <= 500) rankingRanges['Top 500 (401-500)']++;
});

Object.entries(rankingRanges).forEach(([range, count]) => {
    console.log(`   ${range}: ${count} 个应用`);
});

// 评分统计
console.log('\n⭐ 评分统计:');
const ratingStats = {
    '5.0': 0,
    '4.5-4.9': 0,
    '4.0-4.4': 0,
    '3.5-3.9': 0,
    '3.0-3.4': 0,
    '< 3.0': 0
};

data.rankInfo.forEach(app => {
    const rating = app.comment.rating;
    if (rating === 5.0) ratingStats['5.0']++;
    else if (rating >= 4.5) ratingStats['4.5-4.9']++;
    else if (rating >= 4.0) ratingStats['4.0-4.4']++;
    else if (rating >= 3.5) ratingStats['3.5-3.9']++;
    else if (rating >= 3.0) ratingStats['3.0-3.4']++;
    else ratingStats['< 3.0']++;
});

Object.entries(ratingStats).forEach(([range, count]) => {
    console.log(`   ${range} 分: ${count} 个应用`);
});

// 热门发布商
console.log('\n🏢 热门发布商 (Top 10):');
const publisherCount = {};
data.rankInfo.forEach(app => {
    const publisher = app.appInfo.publisher;
    publisherCount[publisher] = (publisherCount[publisher] || 0) + 1;
});

const topPublishers = Object.entries(publisherCount)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10);

topPublishers.forEach(([publisher, count], index) => {
    console.log(`   ${index + 1}. ${publisher}: ${count} 个应用`);
});

// 最新更新的应用
console.log('\n🆕 最近更新的应用 (Top 5):');
const recentApps = data.rankInfo
    .filter(app => app.lastReleaseTime)
    .sort((a, b) => new Date(b.lastReleaseTime) - new Date(a.lastReleaseTime))
    .slice(0, 5);

recentApps.forEach((app, index) => {
    console.log(`   ${index + 1}. ${app.appInfo.appName} - ${app.lastReleaseTime}`);
});

// 数据完整性检查
console.log('\n🔍 数据完整性检查:');
const missingData = {
    noIcon: data.rankInfo.filter(app => !app.appInfo.icon).length,
    noRating: data.rankInfo.filter(app => app.comment.rating === 0).length,
    noComments: data.rankInfo.filter(app => !app.comment.num).length,
    noReleaseTime: data.rankInfo.filter(app => !app.lastReleaseTime).length
};

console.log(`   缺少图标: ${missingData.noIcon} 个应用`);
console.log(`   无评分: ${missingData.noRating} 个应用`);
console.log(`   无评论数: ${missingData.noComments} 个应用`);
console.log(`   无发布时间: ${missingData.noReleaseTime} 个应用`);

console.log('\n' + '='.repeat(80));
console.log('✨ 数据获取任务圆满完成！');
console.log('📁 完整数据已保存至: data/type.json');
console.log('🔗 可以使用其他脚本进一步分析数据');
console.log('='.repeat(80));
