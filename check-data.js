const data = require('./data/type.json');

console.log('='.repeat(60));
console.log('📊 数据统计报告');
console.log('='.repeat(60));

console.log(`📈 总记录数: ${data.metadata.totalRecords}`);
console.log(`✅ 成功页数: ${data.metadata.successPages}`);
console.log(`❌ 失败页数: ${data.metadata.failedPages}`);
console.log(`📅 获取时间: ${data.metadata.fetchDate}`);
console.log(`🏷️  应用类型: 财务类 (genre: ${data.metadata.genre})`);
console.log(`🌍 国家: ${data.metadata.country.toUpperCase()}`);
console.log(`📱 设备: ${data.metadata.device}`);

console.log('\n🏆 前10个应用:');
data.rankInfo.slice(0, 10).forEach((app, i) => {
    console.log(`${i+1}. ${app.appName} (${app.publisher})`);
    console.log(`   App ID: ${app.appId} | 评分: ${app.rating} | 评论数: ${app.num.toLocaleString()}`);
    console.log(`   发布时间: ${app.lastReleaseTime}`);
});

console.log('\n📊 数据结构信息:');
console.log(`   数据字段: app_id, lastReleaseTime, appId, appName, subtitle, publisher, icon, rating, num`);
console.log(`   数据已简化: 移除了所有非核心字段`);
console.log(`   评论数处理: "万"字已转换为实际数值`);

console.log('\n💰 评分最高的5个应用:');
const topRated = data.rankInfo
    .filter(app => app.rating > 0)
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 5);

topRated.forEach((app, i) => {
    console.log(`${i+1}. ${app.appName} - 评分: ${app.rating}`);
});

console.log('\n' + '='.repeat(60));
console.log('数据检查完成 ✨');
console.log('='.repeat(60));
