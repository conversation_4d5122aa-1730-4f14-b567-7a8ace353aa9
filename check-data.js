const data = require('./data/type.json');

console.log('='.repeat(60));
console.log('📊 数据统计报告');
console.log('='.repeat(60));

console.log(`📈 总记录数: ${data.metadata.totalRecords}`);
console.log(`✅ 成功页数: ${data.metadata.successPages}`);
console.log(`❌ 失败页数: ${data.metadata.failedPages}`);
console.log(`📅 获取时间: ${data.metadata.fetchDate}`);
console.log(`🏷️  应用类型: 财务类 (genre: ${data.metadata.genre})`);
console.log(`🌍 国家: ${data.metadata.country.toUpperCase()}`);
console.log(`📱 设备: ${data.metadata.device}`);

console.log('\n🏆 前10个应用:');
data.rankInfo.slice(0, 10).forEach((app, i) => {
    console.log(`${i+1}. ${app.appName} (${app.publisher}) - 第${app.pageNumber}页`);
    console.log(`   排名: ${app.rank_c.ranking} | 评分: ${app.rating} | 评论数: ${app.num.toLocaleString()}`);
});

console.log('\n📄 各页数据统计:');
const pageStats = {};
data.rankInfo.forEach(app => {
    if (!pageStats[app.pageNumber]) {
        pageStats[app.pageNumber] = 0;
    }
    pageStats[app.pageNumber]++;
});

Object.keys(pageStats).sort((a, b) => parseInt(a) - parseInt(b)).forEach(page => {
    console.log(`   第${page}页: ${pageStats[page]}条记录`);
});

console.log('\n💰 评分最高的5个应用:');
const topRated = data.rankInfo
    .filter(app => app.rating > 0)
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 5);

topRated.forEach((app, i) => {
    console.log(`${i+1}. ${app.appName} - 评分: ${app.rating}`);
});

console.log('\n' + '='.repeat(60));
console.log('数据检查完成 ✨');
console.log('='.repeat(60));
