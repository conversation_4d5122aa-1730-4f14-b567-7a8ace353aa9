const axios = require('axios');

/**
 * 请求七麦数据API获取排行榜信息
 * @param {Object} options - 请求参数配置
 * @returns {Promise} - 返回API响应数据
 */
async function fetchRankingData(options = {}) {
    // 默认API URL
    const defaultUrl = 'https://api.qimai.cn/rank/index?analysis=ezEvHiU8MEt5ZXZRKDVwBygcPQUbFCxZUG8hCwMLAwkCPyVFOVkaQ1YND05QWAEDHyhbQUMDAANQUlQOAVAmRFs%3D&brand=free&country=us&genre=36&device=iphone&date=2025-06-25&page=1&is_rank_index=1';
    
    


    // 合并配置选项
    const config = {
        method: 'GET',
        timeout: 10000, // 10秒超时
        headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache'
        },
        ...options
    };

    try {
        console.log('正在请求API:', defaultUrl);
        console.log('请求配置:', JSON.stringify(config, null, 2));
        
        const response = await axios.get(defaultUrl, config);
        
        console.log('请求成功!');
        console.log('状态码:', response.status);
        console.log('响应头:', JSON.stringify(response.headers, null, 2));
        
        return {
            success: true,
            status: response.status,
            headers: response.headers,
            data: response.data
        };
        
    } catch (error) {
        console.error('请求失败:', error.message);
        
        if (error.response) {
            // 服务器响应了错误状态码
            console.error('错误状态码:', error.response.status);
            console.error('错误响应头:', JSON.stringify(error.response.headers, null, 2));
            console.error('错误响应数据:', error.response.data);
            
            return {
                success: false,
                error: 'HTTP_ERROR',
                status: error.response.status,
                message: error.message,
                data: error.response.data
            };
        } else if (error.request) {
            // 请求已发出但没有收到响应
            console.error('网络错误或超时:', error.request);
            
            return {
                success: false,
                error: 'NETWORK_ERROR',
                message: '网络错误或请求超时',
                details: error.message
            };
        } else {
            // 其他错误
            console.error('请求配置错误:', error.message);
            
            return {
                success: false,
                error: 'CONFIG_ERROR',
                message: error.message
            };
        }
    }
}

/**
 * 测试API请求功能
 */
async function testApiRequest() {
    console.log('='.repeat(50));
    console.log('开始测试API请求...');
    console.log('='.repeat(50));
    
    try {
        const result = await fetchRankingData();
        
        if (result.success) {
            console.log('\n✅ API请求测试成功!');
            console.log('响应数据类型:', typeof result.data);
            console.log('响应数据大小:', JSON.stringify(result.data).length, '字符');
            
            // 如果响应数据是对象，显示其结构
            if (typeof result.data === 'object' && result.data !== null) {
                console.log('响应数据结构:');
                console.log(JSON.stringify(result.data, null, 2));
            } else {
                console.log('响应数据:', result.data);
            }
        } else {
            console.log('\n❌ API请求测试失败!');
            console.log('错误类型:', result.error);
            console.log('错误信息:', result.message);
            if (result.data) {
                console.log('错误详情:', result.data);
            }
        }
        
    } catch (error) {
        console.error('\n💥 测试过程中发生异常:', error.message);
        console.error('异常堆栈:', error.stack);
    }
    
    console.log('\n' + '='.repeat(50));
    console.log('测试完成');
    console.log('='.repeat(50));
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
    testApiRequest();
}

// 导出函数供其他模块使用
module.exports = {
    fetchRankingData,
    testApiRequest
};
