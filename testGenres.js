const { fetchMultiplePages, GENRE_MAP } = require('./getTypeList.js');
const fs = require('fs').promises;
const path = require('path');

/**
 * 测试获取几个分类的数据
 */
async function testFewGenres() {
    console.log('🧪 开始测试获取几个分类的应用数据...');
    
    // 只测试前3个分类
    const testGenres = Object.entries(GENRE_MAP).slice(0, 3);
    console.log(`📊 测试分类: ${testGenres.map(([id, name]) => `${name}(${id})`).join(', ')}`);
    
    const allData = [];
    let totalRecords = 0;
    let totalSuccessPages = 0;
    let totalFailedPages = 0;
    
    // 遍历测试分类
    for (const [genreId, genreType] of testGenres) {
        try {
            console.log(`\n${'='.repeat(60)}`);
            console.log(`🎯 开始获取分类: ${genreType} (${genreId})`);
            console.log(`${'='.repeat(60)}`);
            
            // 只获取前2页进行测试
            const result = await fetchMultiplePages(1, 2, null, { genre: genreId });
            
            if (result.success && result.data) {
                allData.push(...result.data.rankInfo);
                totalRecords += result.data.rankInfo.length;
                totalSuccessPages += result.successPages;
                totalFailedPages += result.failedPages;
                
                console.log(`✅ ${genreType} 分类完成: ${result.data.rankInfo.length} 条记录`);
            } else {
                console.log(`❌ ${genreType} 分类获取失败`);
            }
            
            // 分类间添加延迟
            console.log('⏳ 等待2秒后继续下一个分类...');
            await new Promise(resolve => setTimeout(resolve, 2000));
            
        } catch (error) {
            console.error(`💥 获取分类 ${genreType} 时出错:`, error.message);
        }
    }
    
    // 保存测试数据
    try {
        console.log(`\n${'='.repeat(60)}`);
        console.log('💾 正在保存测试数据...');
        
        const finalData = {
            metadata: {
                totalRecords: totalRecords,
                totalGenres: testGenres.length,
                successPages: totalSuccessPages,
                failedPages: totalFailedPages,
                fetchDate: new Date().toISOString(),
                country: 'us',
                device: 'iphone',
                testGenres: Object.fromEntries(testGenres),
                optimizeNote: '测试数据：包含3个分类，移除app_id字段，处理subtitle空值，添加type字段'
            },
            rankInfo: allData
        };
        
        // 确保目录存在
        await fs.mkdir('data', { recursive: true });
        
        // 保存到测试文件
        await fs.writeFile('data/testGenres.json', JSON.stringify(finalData, null, 2), 'utf8');
        
        console.log('✅ 测试数据保存成功!');
        console.log(`📊 总计获取 ${totalRecords} 条记录`);
        console.log(`📂 涵盖 ${testGenres.length} 个分类`);
        console.log(`📁 文件路径: ${path.resolve('data/testGenres.json')}`);
        console.log(`🔧 数据结构: lastReleaseTime, appId, appName, subtitle, publisher, icon, rating, num, type`);
        
        // 显示每个分类的数据统计
        console.log('\n📊 分类数据统计:');
        const typeStats = {};
        allData.forEach(app => {
            typeStats[app.type] = (typeStats[app.type] || 0) + 1;
        });
        
        Object.entries(typeStats).forEach(([type, count]) => {
            console.log(`   ${type}: ${count} 条记录`);
        });
        
        return {
            success: true,
            totalRecords: totalRecords,
            totalGenres: testGenres.length,
            outputFile: 'data/testGenres.json'
        };
        
    } catch (error) {
        console.error('💥 保存测试数据时出错:', error.message);
        return {
            success: false,
            message: error.message
        };
    }
}

// 如果直接运行此文件，则执行测试函数
if (require.main === module) {
    testFewGenres().then(result => {
        if (result.success) {
            console.log('\n🎉 测试完成!');
            console.log(`✅ 成功获取 ${result.totalRecords} 条记录`);
            console.log(`📂 涵盖 ${result.totalGenres} 个分类`);
            console.log(`📁 数据已保存到: ${result.outputFile}`);
        } else {
            console.log('\n❌ 测试失败:', result.message);
        }
    }).catch(error => {
        console.error('\n💥 测试执行异常:', error.message);
    });
}

module.exports = { testFewGenres };
