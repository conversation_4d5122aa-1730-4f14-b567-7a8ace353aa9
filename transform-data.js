const fs = require('fs');
const path = require('path');

/**
 * 处理评论数字段，将包含"万"的数字转换为实际数值
 * @param {string|number} numStr - 原始评论数
 * @returns {number} - 处理后的数值
 */
function processCommentNum(numStr) {
    if (!numStr) return 0;
    
    // 如果已经是数字，直接返回
    if (typeof numStr === 'number') return numStr;
    
    const str = numStr.toString();
    
    // 检查是否包含"万"字
    if (str.includes('万')) {
        // 提取数字部分，去掉"万"字
        const numberPart = str.replace('万', '').replace(',', '');
        const number = parseFloat(numberPart);
        
        // 如果是有效数字，乘以10000（1万）
        if (!isNaN(number)) {
            return Math.round(number * 10000);
        }
    }
    
    // 如果不包含"万"，尝试解析为数字
    const cleanStr = str.replace(/,/g, ''); // 去掉逗号
    const number = parseFloat(cleanStr);
    return isNaN(number) ? 0 : Math.round(number);
}

/**
 * 转换单条应用数据结构
 * @param {Object} app - 原始应用数据
 * @returns {Object} - 转换后的应用数据
 */
function transformAppData(app) {
    return {
        // 保留原有的基本字段
        app_id: app.app_id,
        genre_sub_id: app.genre_sub_id,
        index: app.index,
        
        // 提取并重命名的字段
        lastReleaseTime: app.lastReleaseTime,
        appId: app.appInfo?.appId || app.app_id,
        appName: app.appInfo?.appName || '',
        subtitle: app.appInfo?.subtitle || '',
        publisher: app.appInfo?.publisher || '',
        icon: app.appInfo?.icon || '',
        rating: app.comment?.rating || 0,
        num: processCommentNum(app.comment?.num),
        
        // 保留其他重要字段
        keywordCover: app.keywordCover,
        keywordCoverTop3: app.keywordCoverTop3,
        company: app.company,
        rank_a: app.rank_a,
        rank_b: app.rank_b,
        rank_c: app.rank_c,
        is_ad: app.is_ad,
        
        // 保留我们添加的字段
        pageNumber: app.pageNumber,
        fetchTime: app.fetchTime,
        
        // 保留原始的appInfo和comment对象（可选，用于备份）
        // 如果不需要可以删除这两行
        _original_appInfo: app.appInfo,
        _original_comment: app.comment
    };
}

/**
 * 主转换函数
 */
async function transformData() {
    try {
        console.log('🔄 开始转换数据结构...');
        
        // 读取原始数据
        const rawData = fs.readFileSync('./data/type.json', 'utf8');
        const data = JSON.parse(rawData);
        
        console.log(`📊 原始数据: ${data.rankInfo.length} 条记录`);
        
        // 转换每条记录
        const transformedRankInfo = data.rankInfo.map((app, index) => {
            if (index < 5) {
                // 显示前5条的转换示例
                console.log(`\n📝 转换第${index + 1}条记录:`);
                console.log(`   应用名: ${app.appInfo?.appName}`);
                console.log(`   原始评论数: ${app.comment?.num}`);
                console.log(`   转换后评论数: ${processCommentNum(app.comment?.num)}`);
            }
            
            return transformAppData(app);
        });
        
        // 创建新的数据结构
        const transformedData = {
            metadata: {
                ...data.metadata,
                transformDate: new Date().toISOString(),
                transformNote: '数据结构已优化：提取嵌套字段到顶层，处理评论数万字转换'
            },
            rankInfo: transformedRankInfo
        };
        
        // 保存转换后的数据
        const outputPath = './data/type_transformed.json';
        fs.writeFileSync(outputPath, JSON.stringify(transformedData, null, 2), 'utf8');
        
        console.log('\n✅ 数据转换完成!');
        console.log(`📁 转换后文件: ${path.resolve(outputPath)}`);
        console.log(`📊 转换后记录数: ${transformedData.rankInfo.length}`);
        
        // 显示转换统计
        const stats = {
            totalRecords: transformedData.rankInfo.length,
            withWanChar: 0,
            convertedNums: 0,
            avgRating: 0
        };
        
        let totalRating = 0;
        transformedData.rankInfo.forEach(app => {
            if (data.rankInfo.find(orig => orig.app_id === app.app_id)?.comment?.num?.toString().includes('万')) {
                stats.withWanChar++;
            }
            if (app.num > 10000) {
                stats.convertedNums++;
            }
            totalRating += app.rating;
        });
        
        stats.avgRating = (totalRating / stats.totalRecords).toFixed(2);
        
        console.log('\n📈 转换统计:');
        console.log(`   包含"万"字的记录: ${stats.withWanChar} 条`);
        console.log(`   转换后评论数>1万的记录: ${stats.convertedNums} 条`);
        console.log(`   平均评分: ${stats.avgRating} 分`);
        
        // 显示几个转换示例
        console.log('\n🔍 转换示例 (前3条):');
        transformedData.rankInfo.slice(0, 3).forEach((app, i) => {
            console.log(`${i + 1}. ${app.appName}`);
            console.log(`   appId: ${app.appId}`);
            console.log(`   publisher: ${app.publisher}`);
            console.log(`   rating: ${app.rating}`);
            console.log(`   num: ${app.num.toLocaleString()}`);
            console.log(`   lastReleaseTime: ${app.lastReleaseTime}`);
        });
        
        return transformedData;
        
    } catch (error) {
        console.error('❌ 转换过程中发生错误:', error.message);
        throw error;
    }
}

// 如果直接运行此文件，则执行转换
if (require.main === module) {
    transformData()
        .then(() => {
            console.log('\n🎉 数据转换任务完成!');
            console.log('💡 提示: 可以使用转换后的 data/type_transformed.json 文件');
        })
        .catch(error => {
            console.error('💥 转换失败:', error.message);
            process.exit(1);
        });
}

module.exports = {
    transformData,
    transformAppData,
    processCommentNum
};
