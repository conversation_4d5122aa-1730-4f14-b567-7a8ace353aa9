{"ipadScreenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/PurpleSource116/v4/2a/02/2e/2a022e87-37b0-8029-9a50-69a6392715b4/80ea0b92-c895-4618-bd62-815face6d821_Authenticator.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource116/v4/db/18/ad/db18ad07-cc82-cf1b-3670-92912b400ee3/2529f542-5c62-46cf-8ab7-b0617262b2f8_FaceID.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource126/v4/21/e3/fa/21e3fa52-46f8-e767-bbac-57d690d269de/6ec4ed25-e14b-4d9d-b201-2a1fe631d641_Login_Request.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource116/v4/08/ad/05/08ad050c-2869-6792-60c4-99df5c6e259f/22626e09-44b0-4a60-8d47-b728105cfc48_Push_Notifications.png/576x768bb.png"], "appletvScreenshotUrls": [], "artworkUrl512": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/d5/3f/40/d53f40f8-3510-f467-9328-3635f1f1ac38/AppIcon-0-0-1x_U007emarketing-0-8-0-85-220.png/512x512bb.jpg", "screenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/PurpleSource116/v4/4a/92/c2/4a92c227-ade0-312d-dcc2-97ac1d216c1f/99c1dfcf-a18c-4e09-82cd-8f29437e7935_Authenticator.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource126/v4/6b/f3/1b/6bf31b00-a9c0-be95-d9b8-8ec791f32bcd/105cf7a1-f567-4eb5-86ac-762fe25ba4f8_FaceID.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource126/v4/6e/31/d2/6e31d25f-1c20-926f-d383-2cfb12493aba/36e6d62b-953d-403e-b3fa-17b4081a9572_Login_Request.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/PurpleSource116/v4/20/14/d1/2014d1dc-32e8-447e-4073-a0cbfdca8f7a/0200b112-5d16-4db7-8eac-c8675cc7d44c_Push_Notifications.png/392x696bb.png"], "isGameCenterEnabled": false, "artistViewUrl": "https://apps.apple.com/us/developer/thales-dis-singapore-pte-ltd/id705778388?uo=4", "artworkUrl60": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/d5/3f/40/d53f40f8-3510-f467-9328-3635f1f1ac38/AppIcon-0-0-1x_U007emarketing-0-8-0-85-220.png/60x60bb.jpg", "artworkUrl100": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/d5/3f/40/d53f40f8-3510-f467-9328-3635f1f1ac38/AppIcon-0-0-1x_U007emarketing-0-8-0-85-220.png/100x100bb.jpg", "supportedDevices": ["MacDesktop-MacDesktop", "iPhone5s-iPhone5s", "iPadAir-iPadAir", "iPadAirCellular-iPadAirCellular", "iPadMiniRetina-iPadMiniRetina", "iPadMiniRetinaCellular-iPadMiniRetinaCellular", "iPhone6-iPhone6", "iPhone6Plus-iPhone6Plus", "iPadAir2-iPadAir2", "iPadAir2Cellular-iPadAir2Cellular", "iPadMini3-iPadMini3", "iPadMini3Cellular-iPadMini3Cellular", "iPodTouchSixthGen-iPodTouchSixthGen", "iPhone6s-iPhone6s", "iPhone6sPlus-iPhone6sPlus", "iPadMini4-iPadMini4", "iPadMini4Cellular-iPadMini4Cellular", "iPadPro-iPadPro", "iPadProCellular-iPadProCellular", "iPadPro97-iPadPro97", "iPadPro97Cellular-iPadPro97Cellular", "iPhoneSE-iPhoneSE", "iPhone7-iPhone7", "iPhone7Plus-iPhone7Plus", "iPad611-iPad611", "iPad612-iPad612", "iPad71-iPad71", "iPad72-iPad72", "iPad73-iPad73", "iPad74-iPad74", "iPhone8-iPhone8", "iPhone8Plus-iPhone8Plus", "iPhoneX-iPhoneX", "iPad75-iPad75", "iPad76-iPad76", "iPhoneXS-iPhoneXS", "iPhoneXSMax-iPhoneXSMax", "iPhoneXR-iPhoneXR", "iPad812-iPad812", "iPad834-iPad834", "iPad856-iPad856", "iPad878-iPad878", "Watch4-Watch4", "iPadMini5-iPadMini5", "iPadMini5Cellular-iPadMini5Cellular", "iPadAir3-iPadAir3", "iPadAir3Cellular-iPadAir3Cellular", "iPodTouchSeventhGen-iPodTouchSeventhGen", "iPhone11-iPhone11", "iPhone11Pro-iPhone11Pro", "iPadSeventhGen-iPadSeventhGen", "iPadSeventhGenCellular-iPadSeventhGenCellular", "iPhone11ProMax-iPhone11ProMax", "iPhoneSESecondGen-iPhoneSESecondGen", "iPadProSecondGen-iPadProSecondGen", "iPadProSecondGenCellular-iPadProSecondGenCellular", "iPadProFourthGen-iPadProFourthGen", "iPadProFourthGenCellular-iPadProFourthGenCellular", "iPhone12Mini-iPhone12Mini", "iPhone12-iPhone12", "iPhone12Pro-iPhone12Pro", "iPhone12ProMax-iPhone12ProMax", "iPadAir4-iPadAir4", "iPadAir4Cellular-iPadAir4Cellular", "iPadEighthGen-iPadEighthGen", "iPadEighthGenCellular-iPadEighthGenCellular", "iPadProThirdGen-iPadProThirdGen", "iPadProThirdGenCellular-iPadProThirdGenCellular", "iPadProFifthGen-iPadProFifthGen", "iPadProFifthGenCellular-iPadProFifthGenCellular", "iPhone13Pro-iPhone13Pro", "iPhone13ProMax-iPhone13ProMax", "iPhone13Mini-iPhone13Mini", "iPhone13-iPhone13", "iPadMiniSixthGen-iPadMiniSixthGen", "iPadMiniSixthGenCellular-iPadMiniSixthGenCellular", "iPadNinthGen-iPadNinthGen", "iPadNinthGenCellular-iPadNinthGenCellular", "iPhoneSEThirdGen-iPhoneSEThirdGen", "iPadAirFifthGen-iPadAirFifthGen", "iPadAirFifthGenCellular-iPadAirFifthGenCellular", "iPhone14-iPhone14", "iPhone14Plus-iPhone14Plus", "iPhone14Pro-iPhone14Pro", "iPhone14ProMax-iPhone14ProMax", "iPadTenthGen-iPadTenthGen", "iPadTenthGenCellular-iPadTenthGenCellular", "iPadPro11FourthGen-iPadPro11FourthGen", "iPadPro11FourthGenCellular-iPadPro11FourthGenCellular", "iPadProSixthGen-iPadProSixthGen", "iPadProSixthGenCellular-iPadProSixthGenCellular", "iPhone15-iPhone15", "iPhone15Plus-iPhone15Plus", "iPhone15Pro-iPhone15Pro", "iPhone15ProMax-iPhone15ProMax", "iPadAir11M2-iPadAir11M2", "iPadAir11M2Cellular-iPadAir11M2Cellular", "iPadAir13M2-iPadAir13M2", "iPadAir13M2Cellular-iPadAir13M2Cellular", "iPadPro11M4-iPadPro11M4", "iPadPro11M4Cellular-iPadPro11M4Cellular", "iPadPro13M4-iPadPro13M4", "iPadPro13M4Cellular-iPadPro13M4Cellular", "iPhone16-iPhone16", "iPhone16Plus-iPhone16Plus", "iPhone16Pro-iPhone16Pro", "iPhone16ProMax-iPhone16ProMax", "iPadMiniA17Pro-iPadMiniA17Pro", "iPadMiniA17ProCellular-iPadMiniA17ProCellular", "iPhone16e-iPhone16e", "iPadA16-iPadA16", "iPadA16Cellular-iPadA16Cellular", "iPadAir11M3-iPadAir11M3", "iPadAir11M3Cellular-iPadAir11M3Cellular", "iPadAir13M3-iPadAir13M3", "iPadAir13M3Cellular-iPadAir13M3Cellular"], "features": ["iosUniversal"], "advisories": [], "kind": "software", "sellerUrl": "https://cpl.thalesgroup.com/access-management/authenticators/software-authentication/mobilepass-plus-push-authentication", "languageCodesISO2A": ["NL", "EN", "FR", "DE", "IT", "JA", "PT", "ZH", "ES", "ZH"], "fileSizeBytes": "44452864", "formattedPrice": "Free", "userRatingCountForCurrentVersion": 108861, "trackContentRating": "4+", "averageUserRatingForCurrentVersion": 4.77786, "averageUserRating": 4.77786, "trackCensoredName": "SafeNet MobilePASS+", "trackViewUrl": "https://apps.apple.com/us/app/safenet-mobilepass/id**********?uo=4", "contentAdvisoryRating": "4+", "artistId": 705778388, "artistName": "Thales DIS (Singapore) Pte Ltd", "genres": ["Business", "Productivity"], "price": 0, "sellerName": "Thales DIS (Singapore) Pte Ltd", "bundleId": "com.gemalto.mobilepassp", "primaryGenreName": "Business", "primaryGenreId": 6000, "isVppDeviceBasedLicensingEnabled": true, "genreIds": ["6000", "6007"], "releaseDate": "2015-12-18T21:40:44Z", "currentVersionReleaseDate": "2025-06-18T11:56:08Z", "releaseNotes": "- End-User License Agreement (EULA) update\n- Accessibility improvement\n- Security fix", "version": "iOS App Version 2.6.1", "wrapperType": "software", "currency": "USD", "description": "SafeNet MobilePASS+ is a next generation mobile authenticator offering the best in user-experience and security. Approve push authentication requests with a single-tap and generate secure one-time passcodes. Unlock authenticators with TouchID or FaceID. Quick and easy self-enrollment process with QR code activation with simple to follow instructions.\n\nUse SafeNet MobilePASS+ with leading cloud apps, security gateways and VPNs.\nAdd 3rd party authenticators for various applications to protect your personal and professional accounts. \n\nPermissions\nSafeNet MobilePASS+ requires access to the camera only if enrollment via QR Code is enabled.", "minimumOsVersion": "16.0", "trackId": **********, "trackName": "SafeNet MobilePASS+", "userRatingCount": 108861}