const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');

/**
 * 处理评论数字段，将包含"万"的数字转换为实际数值
 * @param {string|number} numStr - 原始评论数
 * @returns {number} - 处理后的数值
 */
function processCommentNum(numStr) {
    if (!numStr) return 0;

    // 如果已经是数字，直接返回
    if (typeof numStr === 'number') return numStr;

    const str = numStr.toString();

    // 检查是否包含"万"字
    if (str.includes('万')) {
        // 提取数字部分，去掉"万"字
        const numberPart = str.replace('万', '').replace(',', '');
        const number = parseFloat(numberPart);

        // 如果是有效数字，乘以10000（1万）
        if (!isNaN(number)) {
            return Math.round(number * 10000);
        }
    }

    // 如果不包含"万"，尝试解析为数字
    const cleanStr = str.replace(/,/g, ''); // 去掉逗号
    const number = parseFloat(cleanStr);
    return isNaN(number) ? 0 : Math.round(number);
}

// 应用分类映射
const GENRE_MAP = {
    6021: 'Magazines & Newspapers',
    6015: 'Finance',
    6006: 'Reference',
    6010: 'Navigation',
    6061: 'Kids',
    6002: 'Utilities',
    6024: 'Shopping',
    6013: 'Health & Fitness',
    6017: 'Education',
    6003: 'Travel',
    6023: 'Food & Drink',
    6026: 'Developer Tools',
    6000: 'Business',
    6005: 'Social Networking',
    6008: 'Photo & Video',
    6012: 'Lifestyle',
    6004: 'Sports',
    6001: 'Weather',
    6018: 'Books',
    6027: 'Graphics & Design',
    6007: 'Productivity',
    6020: 'Medical',
    6011: 'Music',
    6016: 'Entertainment'
};

/**
 * 优化应用数据，只保留最终需要的字段
 * @param {Object} app - 原始应用数据
 * @param {string} type - 应用分类类型
 * @returns {Object} - 优化后的应用数据
 */
function optimizeAppData(app, type) {
    // 处理subtitle字段
    let subtitle = '';
    if (app.appInfo?.subtitle && app.appInfo.subtitle !== '暂无数据') {
        subtitle = app.appInfo.subtitle;
    }

    return {
        lastReleaseTime: app.lastReleaseTime,
        appId: app.appInfo?.appId || app.app_id,
        appName: app.appInfo?.appName || '',
        subtitle: subtitle,
        publisher: app.appInfo?.publisher || '',
        icon: app.appInfo?.icon || '',
        rating: app.comment?.rating || 0,
        num: processCommentNum(app.comment?.num),
        type: type
    };
}

/**
 * 请求七麦数据API获取排行榜信息
 * @param {number} page - 页码
 * @param {Object} options - 请求参数配置
 * @returns {Promise} - 返回API响应数据
 */
async function fetchRankingData(page = 1, options = {}) {
    // 新的API URL，使用传入的页码
    const baseUrl = 'https://api.qimai.cn/rank/index';
    const params = new URLSearchParams({
        analysis: 'ezEvECUSLA54dW4XKQtgBCgiIlk2SyBMa2A1FAJ%2BXhMECj5JGg0IDHdASxNYWA5JDgYcHAJ1Eg9aUlgOAFVWUFlMOVkG',
        brand: 'free',
        device: 'iphone',
        country: 'us',
        genre: options.genre || '6015', // 支持传入genre参数
        date: '2025-06-26',
        page: page.toString(),
        is_rank_index: '1'
    });
    const apiUrl = `${baseUrl}?${params.toString()}`;

    // 合并配置选项，使用登录后的请求头
    const config = {
        method: 'GET',
        timeout: 15000, // 15秒超时
        headers: {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Connection': 'keep-alive',
            'Cookie': 'qm_check=A1sdRUIQChtxen8pI0dANi8zcX5zHBl+YnEhLyZIPxw8WkVRVRliYGBFVFdeSFlSBU8CAAkABX5VXk5IPBAIUFRGA30BBRgTFThdJ0laRURtBWYAGBghVSNZSVBYGRVRWFxTXxpfRFdESFVKGQceABtN; gr_user_id=2c37cfbf-db89-4d0d-a44e-ccdf7e74856d; AUTHKEY=3HZau89Fl3pvUXS9sjPzPzb0TWeBbpwm03fmRP5q2GbqzRdF4iyzvKeGpXTqvEScCF4cjXKCdyEI%2F1Iy5mYSRtWzIzhO8Pr9mb7Hl7S%2Fp6b%2FrWkr5lVlTQ%3D%3D; ada35577182650f1_gr_last_sent_cs1=qm22135790360; PHPSESSID=keclb9f818j2m6fopakftvt1a1; USERINFO=RSbgnssfcGhrWY%2F%2Bo8c5vSCY2X8wRAo1EswIyouTzQeYVg1U19087jfL5Sf6XUqdUoEf38FpHikt243dbBajZPSkFoaxLF0ls%2FgjuSfOevGSO4QpLM2Usf4qQbN0EHaqGy%2BUWciyb6%2BKxr9XpJI5ewylGZOSMNR1; ada35577182650f1_gr_session_id=2bac8358-6b58-4bbe-9c61-300b7b002127; ada35577182650f1_gr_last_sent_sid_with_cs1=2bac8358-6b58-4bbe-9c61-300b7b002127; ada35577182650f1_gr_session_id_sent_vst=2bac8358-6b58-4bbe-9c61-300b7b002127; synct=1750921496.120; syncd=-255; ada35577182650f1_gr_cs1=qm22135790360',
            'Origin': 'https://www.qimai.cn',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        },
        ...options
    };

    try {
        console.log(`正在请求API第${page}页:`, apiUrl);

        const response = await axios.get(apiUrl, config);
        
        console.log('请求成功!');
        console.log('状态码:', response.status);
        console.log('响应头:', JSON.stringify(response.headers, null, 2));
        
        return {
            success: true,
            status: response.status,
            headers: response.headers,
            data: response.data
        };
        
    } catch (error) {
        console.error('请求失败:', error.message);
        
        if (error.response) {
            // 服务器响应了错误状态码
            console.error('错误状态码:', error.response.status);
            console.error('错误响应头:', JSON.stringify(error.response.headers, null, 2));
            console.error('错误响应数据:', error.response.data);
            
            return {
                success: false,
                error: 'HTTP_ERROR',
                status: error.response.status,
                message: error.message,
                data: error.response.data
            };
        } else if (error.request) {
            // 请求已发出但没有收到响应
            console.error('网络错误或超时:', error.request);
            
            return {
                success: false,
                error: 'NETWORK_ERROR',
                message: '网络错误或请求超时',
                details: error.message
            };
        } else {
            // 其他错误
            console.error('请求配置错误:', error.message);
            
            return {
                success: false,
                error: 'CONFIG_ERROR',
                message: error.message
            };
        }
    }
}

/**
 * 批量获取多页数据并保存到文件
 * @param {number} startPage - 起始页码
 * @param {number} endPage - 结束页码
 * @param {string} outputFile - 输出文件路径
 * @returns {Promise} - 返回处理结果
 */
async function fetchMultiplePages(startPage = 1, endPage = 10, outputFile = 'data/typeList.json', options = {}) {
    const genre = options.genre || '6015';
    const genreType = GENRE_MAP[genre] || 'Unknown';

    console.log('='.repeat(60));
    console.log(`🚀 开始批量获取第${startPage}-${endPage}页数据`);
    console.log(`📂 分类: ${genreType} (${genre})`);
    console.log('='.repeat(60));

    const allRankInfo = [];
    let successCount = 0;
    let failCount = 0;

    for (let page = startPage; page <= endPage; page++) {
        try {
            console.log(`\n📄 正在获取第${page}页数据...`);

            const result = await fetchRankingData(page, { genre });

            if (result.success && result.data) {
                // 检查是否需要登录
                if (result.data.code === 10011) {
                    console.log(`🔒 第${page}页需要登录: ${result.data.msg}`);
                    console.log(`ℹ️  已获取前${page-1}页数据，后续页面需要登录权限`);
                    failCount++;
                    // 如果遇到登录限制，可以选择停止继续请求
                    // break; // 取消注释这行可以在遇到登录限制时停止
                } else if (result.data.rankInfo) {
                    const rankInfo = result.data.rankInfo;
                    console.log(`✅ 第${page}页获取成功，共${rankInfo.length}条数据`);

                    // 为每条数据添加页码信息
                    const pageData = rankInfo.map(item => ({
                        ...item,
                        pageNumber: page,
                        fetchTime: new Date().toISOString()
                    }));

                    allRankInfo.push(...pageData);
                    successCount++;

                    // 显示当前页的前3个应用
                    if (rankInfo.length > 0) {
                        console.log(`   前3个应用: ${rankInfo.slice(0, 3).map(app => app.appInfo.appName).join(', ')}`);
                    }
                } else {
                    console.log(`❌ 第${page}页数据格式异常: ${result.data.msg || '无rankInfo字段'}`);
                    failCount++;
                }
            } else {
                console.log(`❌ 第${page}页获取失败:`, result.message || '未知错误');
                failCount++;
            }

            // 添加延迟避免请求过于频繁
            if (page < endPage) {
                console.log('⏳ 等待2秒后继续...');
                await new Promise(resolve => setTimeout(resolve, 2000));
            }

        } catch (error) {
            console.error(`💥 第${page}页请求异常:`, error.message);
            failCount++;
        }
    }

    // 优化数据结构并保存到文件
    try {
        console.log(`\n💾 正在优化数据结构...`);

        // 优化每条记录，添加type字段
        const optimizedRankInfo = allRankInfo.map(app => optimizeAppData(app, genreType));

        // 构建数据对象
        const dataToSave = {
            metadata: {
                totalRecords: optimizedRankInfo.length,
                pageRange: `${startPage}-${endPage}`,
                successPages: successCount,
                failedPages: failCount,
                fetchDate: new Date().toISOString(),
                genre: genre,
                genreType: genreType,
                country: 'us',
                device: 'iphone',
                optimizeNote: '数据已优化：移除app_id字段，处理subtitle空值，添加type字段，只保留9个核心字段'
            },
            rankInfo: optimizedRankInfo
        };

        // 如果指定了输出文件，则保存到文件
        if (outputFile) {
            console.log(`💾 正在保存数据到 ${outputFile}...`);

            // 确保目录存在
            const dir = path.dirname(outputFile);
            await fs.mkdir(dir, { recursive: true });

            await fs.writeFile(outputFile, JSON.stringify(dataToSave, null, 2), 'utf8');

            console.log('✅ 数据保存成功!');
            console.log(`📊 总计获取 ${optimizedRankInfo.length} 条记录`);
            console.log(`📁 文件路径: ${path.resolve(outputFile)}`);
            console.log('🔧 数据结构: lastReleaseTime, appId, appName, subtitle, publisher, icon, rating, num, type');
        }

        return {
            success: true,
            totalRecords: optimizedRankInfo.length,
            successPages: successCount,
            failedPages: failCount,
            outputFile: outputFile,
            data: dataToSave
        };

    } catch (error) {
        console.error('💥 保存文件失败:', error.message);
        return {
            success: false,
            error: error.message,
            totalRecords: allRankInfo.length
        };
    }
}

/**
 * 测试API请求功能
 */
async function testApiRequest() {
    console.log('='.repeat(50));
    console.log('开始测试API请求...');
    console.log('='.repeat(50));
    
    try {
        const result = await fetchRankingData();
        
        if (result.success) {
            console.log('\n✅ API请求测试成功!');
            console.log('响应数据类型:', typeof result.data);
            console.log('响应数据大小:', JSON.stringify(result.data).length, '字符');
            
            // 如果响应数据是对象，显示其结构
            if (typeof result.data === 'object' && result.data !== null) {
                console.log('响应数据结构:');
                console.log(JSON.stringify(result.data, null, 2));
            } else {
                console.log('响应数据:', result.data);
            }
        } else {
            console.log('\n❌ API请求测试失败!');
            console.log('错误类型:', result.error);
            console.log('错误信息:', result.message);
            if (result.data) {
                console.log('错误详情:', result.data);
            }
        }
        
    } catch (error) {
        console.error('\n💥 测试过程中发生异常:', error.message);
        console.error('异常堆栈:', error.stack);
    }
    
    console.log('\n' + '='.repeat(50));
    console.log('测试完成');
    console.log('='.repeat(50));
}

/**
 * 获取免费可用的数据页面（通常是前4页）
 */
async function fetchFreePages(outputFile = 'data/type.json') {
    console.log('🆓 开始获取免费可用的数据页面...');
    return await fetchMultiplePages(1, 4, outputFile);
}

/**
 * 获取所有分类的数据并合并保存
 */
async function fetchAllGenres() {
    console.log('🌟 开始获取所有分类的应用数据...');
    console.log(`📊 总共需要获取 ${Object.keys(GENRE_MAP).length} 个分类`);

    const allData = [];
    let totalRecords = 0;
    let totalSuccessPages = 0;
    let totalFailedPages = 0;

    // 遍历所有分类
    for (const [genreId, genreType] of Object.entries(GENRE_MAP)) {
        try {
            console.log(`\n${'='.repeat(80)}`);
            console.log(`🎯 开始获取分类: ${genreType} (${genreId})`);
            console.log(`${'='.repeat(80)}`);

            const result = await fetchMultiplePages(1, 10, null, { genre: genreId });

            if (result.success && result.data) {
                allData.push(...result.data.rankInfo);
                totalRecords += result.data.rankInfo.length;
                totalSuccessPages += result.successPages;
                totalFailedPages += result.failedPages;

                console.log(`✅ ${genreType} 分类完成: ${result.data.rankInfo.length} 条记录`);
            } else {
                console.log(`❌ ${genreType} 分类获取失败`);
            }

            // 分类间添加延迟
            console.log('⏳ 等待3秒后继续下一个分类...');
            await new Promise(resolve => setTimeout(resolve, 3000));

        } catch (error) {
            console.error(`💥 获取分类 ${genreType} 时出错:`, error.message);
        }
    }

    // 保存合并后的数据
    try {
        console.log(`\n${'='.repeat(80)}`);
        console.log('💾 正在保存合并后的数据...');

        const finalData = {
            metadata: {
                totalRecords: totalRecords,
                totalGenres: Object.keys(GENRE_MAP).length,
                successPages: totalSuccessPages,
                failedPages: totalFailedPages,
                fetchDate: new Date().toISOString(),
                country: 'us',
                device: 'iphone',
                genres: GENRE_MAP,
                optimizeNote: '数据已优化：包含所有分类，移除app_id字段，处理subtitle空值，添加type字段'
            },
            rankInfo: allData
        };

        // 确保目录存在
        await fs.mkdir('data', { recursive: true });

        // 保存到文件
        await fs.writeFile('data/typeList.json', JSON.stringify(finalData, null, 2), 'utf8');

        console.log('✅ 数据保存成功!');
        console.log(`📊 总计获取 ${totalRecords} 条记录`);
        console.log(`📂 涵盖 ${Object.keys(GENRE_MAP).length} 个分类`);
        console.log(`📁 文件路径: ${path.resolve('data/typeList.json')}`);
        console.log(`🔧 数据结构: lastReleaseTime, appId, appName, subtitle, publisher, icon, rating, num, type`);

        return {
            success: true,
            totalRecords: totalRecords,
            totalGenres: Object.keys(GENRE_MAP).length,
            outputFile: 'data/typeList.json'
        };

    } catch (error) {
        console.error('💥 保存数据时出错:', error.message);
        return {
            success: false,
            message: error.message
        };
    }
}

/**
 * 主函数 - 获取所有分类的1-10页数据并保存
 */
async function main() {
    try {
        const result = await fetchAllGenres();

        if (result.success) {
            console.log('\n🎉 所有分类数据获取完成!');
            console.log(`✅ 成功获取 ${result.totalRecords} 条记录`);
            console.log(`📂 涵盖 ${result.totalGenres} 个分类`);
            console.log(`📁 数据已保存到: ${result.outputFile}`);
        } else {
            console.log('\n❌ 数据获取失败:', result.message);
        }
    } catch (error) {
        console.error('\n💥 程序执行异常:', error.message);
    }
}

// 如果直接运行此文件，则执行主函数
if (require.main === module) {
    main();
}

// 导出函数供其他模块使用
module.exports = {
    fetchRankingData,
    fetchMultiplePages,
    fetchFreePages,
    fetchAllGenres,
    testApiRequest,
    main,
    GENRE_MAP
};
