const { fetchAllGenres, GENRE_MAP } = require('./getTypeList.js');

console.log('🌟 准备获取所有分类的应用数据...');
console.log(`📊 总共需要获取 ${Object.keys(GENRE_MAP).length} 个分类`);
console.log('⏰ 预计耗时: 20-30分钟');
console.log('\n📂 将要获取的分类:');

Object.entries(GENRE_MAP).forEach(([id, name], index) => {
    console.log(`   ${index + 1}. ${name} (${id})`);
});

console.log('\n💡 每个分类将获取1-10页数据（约500条记录）');
console.log('📊 预计总记录数: ~12,000条');
console.log('📁 最终文件大小: ~5-6MB');

console.log('\n⚠️  注意事项:');
console.log('   - 请确保网络连接稳定');
console.log('   - 程序会自动添加请求间隔，避免被限制');
console.log('   - 可以随时按 Ctrl+C 中断程序');
console.log('   - 数据将保存到 data/typeList.json');

console.log('\n🚀 开始执行...');

// 执行完整的数据获取
fetchAllGenres().then(result => {
    if (result.success) {
        console.log('\n🎉 所有分类数据获取完成!');
        console.log(`✅ 成功获取 ${result.totalRecords} 条记录`);
        console.log(`📂 涵盖 ${result.totalGenres} 个分类`);
        console.log(`📁 数据已保存到: ${result.outputFile}`);
        
        console.log('\n📊 建议下一步操作:');
        console.log('   1. 运行 node checkData.js 查看数据统计');
        console.log('   2. 检查 data/typeList.json 文件');
        console.log('   3. 根据需要进行数据分析');
    } else {
        console.log('\n❌ 数据获取失败:', result.message);
    }
}).catch(error => {
    console.error('\n💥 程序执行异常:', error.message);
    console.log('\n🔧 可能的解决方案:');
    console.log('   1. 检查网络连接');
    console.log('   2. 重新运行程序');
    console.log('   3. 检查API认证信息是否有效');
});
