const fs = require('fs');

// 读取测试数据文件
const data = JSON.parse(fs.readFileSync('./data/testGenres.json', 'utf8'));

console.log('============================================================');
console.log('📊 多分类数据统计报告');
console.log('============================================================');
console.log(`📈 总记录数: ${data.metadata.totalRecords}`);
console.log(`📂 分类数量: ${data.metadata.totalGenres}`);
console.log(`✅ 成功页数: ${data.metadata.successPages}`);
console.log(`❌ 失败页数: ${data.metadata.failedPages}`);
console.log(`📅 获取时间: ${new Date(data.metadata.fetchDate).toLocaleString('zh-CN')}`);
console.log(`🌍 国家: ${data.metadata.country.toUpperCase()}`);
console.log(`📱 设备: ${data.metadata.device}`);

console.log('\n📂 测试分类:');
Object.entries(data.metadata.testGenres).forEach(([id, name]) => {
    console.log(`   ${name} (${id})`);
});

console.log('\n📊 各分类数据统计:');
const typeStats = {};
data.rankInfo.forEach(app => {
    typeStats[app.type] = (typeStats[app.type] || 0) + 1;
});

Object.entries(typeStats).forEach(([type, count]) => {
    console.log(`   ${type}: ${count} 条记录`);
});

console.log('\n🏆 每个分类的前3个应用:');
Object.keys(data.metadata.testGenres).forEach(genreId => {
    const genreName = data.metadata.testGenres[genreId];
    const genreApps = data.rankInfo.filter(app => app.type === genreName).slice(0, 3);
    
    console.log(`\n📱 ${genreName} 分类:`);
    genreApps.forEach((app, i) => {
        console.log(`   ${i+1}. ${app.appName} (${app.publisher})`);
        console.log(`      App ID: ${app.appId} | 评分: ${app.rating} | 评论数: ${app.num.toLocaleString()}`);
        console.log(`      发布时间: ${app.lastReleaseTime}`);
    });
});

console.log('\n💰 各分类评分最高的应用:');
Object.keys(data.metadata.testGenres).forEach(genreId => {
    const genreName = data.metadata.testGenres[genreId];
    const genreApps = data.rankInfo.filter(app => app.type === genreName && app.rating > 0);
    
    if (genreApps.length > 0) {
        const topRated = genreApps.sort((a, b) => b.rating - a.rating)[0];
        console.log(`   ${genreName}: ${topRated.appName} - 评分: ${topRated.rating}`);
    }
});

console.log('\n🔥 各分类最受欢迎的应用 (按评论数):');
Object.keys(data.metadata.testGenres).forEach(genreId => {
    const genreName = data.metadata.testGenres[genreId];
    const genreApps = data.rankInfo.filter(app => app.type === genreName);
    
    if (genreApps.length > 0) {
        const topByComments = genreApps.sort((a, b) => b.num - a.num)[0];
        console.log(`   ${genreName}: ${topByComments.appName}`);
        console.log(`      发布商: ${topByComments.publisher}`);
        console.log(`      评论数: ${topByComments.num.toLocaleString()}`);
        console.log(`      评分: ${topByComments.rating}`);
    }
});

// 文件大小信息
const fileSize = fs.statSync('./data/testGenres.json').size;
console.log('\n📁 文件信息:');
console.log(`   文件大小: ${(fileSize / 1024).toFixed(2)} KB`);
console.log(`   平均每条记录: ${(fileSize / data.rankInfo.length).toFixed(0)} 字节`);

console.log('\n🔧 数据结构验证:');
const sampleApp = data.rankInfo[0];
const fields = Object.keys(sampleApp);
console.log(`   字段数量: ${fields.length}`);
console.log(`   字段列表: ${fields.join(', ')}`);

// 验证type字段是否正确添加
const hasTypeField = data.rankInfo.every(app => app.type);
console.log(`   type字段完整性: ${hasTypeField ? '✅ 完整' : '❌ 缺失'}`);

// 验证不同分类的type值
const uniqueTypes = [...new Set(data.rankInfo.map(app => app.type))];
console.log(`   分类类型: ${uniqueTypes.join(', ')}`);

console.log('\n============================================================');
console.log('多分类数据检查完成 ✨');
console.log('============================================================');
