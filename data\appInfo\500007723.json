{"artistViewUrl": "https://apps.apple.com/us/developer/fortinet/id345361031?uo=4", "artworkUrl60": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/ab/6f/80/ab6f80fb-4421-e0be-627e-5ba0c8da766f/AppIcon-0-0-1x_U007emarketing-0-7-0-0-85-220.png/60x60bb.jpg", "artworkUrl100": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/ab/6f/80/ab6f80fb-4421-e0be-627e-5ba0c8da766f/AppIcon-0-0-1x_U007emarketing-0-7-0-0-85-220.png/100x100bb.jpg", "screenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/Purple113/v4/c0/cc/e1/c0cce187-6ea0-8759-1b6b-fcdf40b93af8/mzl.flgllncp.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple123/v4/0d/b8/ea/0db8ea1c-6fe5-b031-6664-39245aeb01df/mzl.qtifknaz.jpg/392x696bb.jpg", "https://is1-ssl.mzstatic.com/image/thumb/Purple113/v4/1e/13/08/1e130813-9d03-eadb-5636-0f91799f780f/mzl.yiveemct.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple123/v4/ac/1f/e5/ac1fe524-09a4-3b6f-5ee6-59d3189a3531/mzl.alfjmapp.png/392x696bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple123/v4/89/7e/2e/897e2ec3-3362-0808-9652-90ca81d5a0f3/mzl.sxcvijaa.png/392x696bb.png"], "ipadScreenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/Purple113/v4/02/4b/29/024b29f7-6379-2c60-4f68-023648e423a3/mzl.gjizvvyl.jpg/576x768bb.jpg", "https://is1-ssl.mzstatic.com/image/thumb/Purple113/v4/4d/49/ac/4d49ac79-b05e-49b0-bbb8-6ca18403b219/mzl.kukydstu.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple113/v4/c3/08/7e/c3087e66-9fa6-5377-5622-18d12dc89261/mzl.sgptbfet.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple113/v4/d6/44/d0/d644d0e9-0cbd-512f-0a97-9186e9dd234b/mzl.uidoxuov.png/576x768bb.png"], "appletvScreenshotUrls": [], "artworkUrl512": "https://is1-ssl.mzstatic.com/image/thumb/Purple211/v4/ab/6f/80/ab6f80fb-4421-e0be-627e-5ba0c8da766f/AppIcon-0-0-1x_U007emarketing-0-7-0-0-85-220.png/512x512bb.jpg", "isGameCenterEnabled": false, "features": ["iosUniversal"], "advisories": [], "kind": "software", "supportedDevices": ["iPhone5s-iPhone5s", "iPadAir-iPadAir", "iPadAirCellular-iPadAirCellular", "iPadMiniRetina-iPadMiniRetina", "iPadMiniRetinaCellular-iPadMiniRetinaCellular", "iPhone6-iPhone6", "iPhone6Plus-iPhone6Plus", "iPadAir2-iPadAir2", "iPadAir2Cellular-iPadAir2Cellular", "iPadMini3-iPadMini3", "iPadMini3Cellular-iPadMini3Cellular", "iPodTouchSixthGen-iPodTouchSixthGen", "iPhone6s-iPhone6s", "iPhone6sPlus-iPhone6sPlus", "iPadMini4-iPadMini4", "iPadMini4Cellular-iPadMini4Cellular", "iPadPro-iPadPro", "iPadProCellular-iPadProCellular", "iPadPro97-iPadPro97", "iPadPro97Cellular-iPadPro97Cellular", "iPhoneSE-iPhoneSE", "iPhone7-iPhone7", "iPhone7Plus-iPhone7Plus", "iPad611-iPad611", "iPad612-iPad612", "iPad71-iPad71", "iPad72-iPad72", "iPad73-iPad73", "iPad74-iPad74", "iPhone8-iPhone8", "iPhone8Plus-iPhone8Plus", "iPhoneX-iPhoneX", "iPad75-iPad75", "iPad76-iPad76", "iPhoneXS-iPhoneXS", "iPhoneXSMax-iPhoneXSMax", "iPhoneXR-iPhoneXR", "iPad812-iPad812", "iPad834-iPad834", "iPad856-iPad856", "iPad878-iPad878", "iPadMini5-iPadMini5", "iPadMini5Cellular-iPadMini5Cellular", "iPadAir3-iPadAir3", "iPadAir3Cellular-iPadAir3Cellular", "iPodTouchSeventhGen-iPodTouchSeventhGen", "iPhone11-iPhone11", "iPhone11Pro-iPhone11Pro", "iPadSeventhGen-iPadSeventhGen", "iPadSeventhGenCellular-iPadSeventhGenCellular", "iPhone11ProMax-iPhone11ProMax", "iPhoneSESecondGen-iPhoneSESecondGen", "iPadProSecondGen-iPadProSecondGen", "iPadProSecondGenCellular-iPadProSecondGenCellular", "iPadProFourthGen-iPadProFourthGen", "iPadProFourthGenCellular-iPadProFourthGenCellular", "iPhone12Mini-iPhone12Mini", "iPhone12-iPhone12", "iPhone12Pro-iPhone12Pro", "iPhone12ProMax-iPhone12ProMax", "iPadAir4-iPadAir4", "iPadAir4Cellular-iPadAir4Cellular", "iPadEighthGen-iPadEighthGen", "iPadEighthGenCellular-iPadEighthGenCellular", "iPadProThirdGen-iPadProThirdGen", "iPadProThirdGenCellular-iPadProThirdGenCellular", "iPadProFifthGen-iPadProFifthGen", "iPadProFifthGenCellular-iPadProFifthGenCellular", "iPhone13Pro-iPhone13Pro", "iPhone13ProMax-iPhone13ProMax", "iPhone13Mini-iPhone13Mini", "iPhone13-iPhone13", "iPadMiniSixthGen-iPadMiniSixthGen", "iPadMiniSixthGenCellular-iPadMiniSixthGenCellular", "iPadNinthGen-iPadNinthGen", "iPadNinthGenCellular-iPadNinthGenCellular", "iPhoneSEThirdGen-iPhoneSEThirdGen", "iPadAirFifthGen-iPadAirFifthGen", "iPadAirFifthGenCellular-iPadAirFifthGenCellular", "iPhone14-iPhone14", "iPhone14Plus-iPhone14Plus", "iPhone14Pro-iPhone14Pro", "iPhone14ProMax-iPhone14ProMax", "iPadTenthGen-iPadTenthGen", "iPadTenthGenCellular-iPadTenthGenCellular", "iPadPro11FourthGen-iPadPro11FourthGen", "iPadPro11FourthGenCellular-iPadPro11FourthGenCellular", "iPadProSixthGen-iPadProSixthGen", "iPadProSixthGenCellular-iPadProSixthGenCellular", "iPhone15-iPhone15", "iPhone15Plus-iPhone15Plus", "iPhone15Pro-iPhone15Pro", "iPhone15ProMax-iPhone15ProMax", "iPadAir11M2-iPadAir11M2", "iPadAir11M2Cellular-iPadAir11M2Cellular", "iPadAir13M2-iPadAir13M2", "iPadAir13M2Cellular-iPadAir13M2Cellular", "iPadPro11M4-iPadPro11M4", "iPadPro11M4Cellular-iPadPro11M4Cellular", "iPadPro13M4-iPadPro13M4", "iPadPro13M4Cellular-iPadPro13M4Cellular", "iPhone16-iPhone16", "iPhone16Plus-iPhone16Plus", "iPhone16Pro-iPhone16Pro", "iPhone16ProMax-iPhone16ProMax", "iPadMiniA17Pro-iPadMiniA17Pro", "iPadMiniA17ProCellular-iPadMiniA17ProCellular", "iPhone16e-iPhone16e", "iPadA16-iPadA16", "iPadA16Cellular-iPadA16Cellular", "iPadAir11M3-iPadAir11M3", "iPadAir11M3Cellular-iPadAir11M3Cellular", "iPadAir13M3-iPadAir13M3", "iPadAir13M3Cellular-iPadAir13M3Cellular"], "trackContentRating": "4+", "trackCensoredName": "FortiToken Mobile", "trackViewUrl": "https://apps.apple.com/us/app/fortitoken-mobile/id500007723?uo=4", "contentAdvisoryRating": "4+", "averageUserRating": 2.84259, "averageUserRatingForCurrentVersion": 2.84259, "sellerUrl": "http://video.fortinet.com/video/122", "languageCodesISO2A": ["EN", "FR", "JA", "ZH", "ZH"], "fileSizeBytes": "12404736", "formattedPrice": "Free", "userRatingCountForCurrentVersion": 108, "artistId": 345361031, "artistName": "Fortinet", "genres": ["Business", "Utilities"], "price": 0, "bundleId": "FortiToken-Mobile", "currentVersionReleaseDate": "2025-06-05T05:38:23Z", "genreIds": ["6000", "6002"], "trackId": 500007723, "trackName": "FortiToken Mobile", "releaseDate": "2012-02-10T00:36:10Z", "sellerName": "Fortinet, Inc", "releaseNotes": "Bug fix:\n- Fixed token transfer failures when tokens are from different regions.", "version": "5.5.2", "wrapperType": "software", "currency": "USD", "description": "FortiToken Mobile is an OATH compliant, event-based and time-based One Time Password (OTP) generator application for the mobile device. It is the client component of Fortinet’s highly secure, simple to use and administer, and extremely cost effective solution for meeting your strong authentication needs. You will need to use FortiOS or FortiAuthenticator as the back-end validation server.\n\nRequires iOS 9 or later. Compatible with iPhone, iPad, and iPod touch.\n\nPrivacy and Control:\nFortiToken Mobile cannot change settings on your phone, take pictures or video, record or transmit audio, nor can it read or send emails. Further, it cannot see your browser history, and it requires your permission to send you notifications or to change any settings. And, FortiToken Mobile cannot remotely wipe your phone. Any visibility FortiToken Mobile requires is to verify your OS version to determine app version compatibility.  Sensitive information, such as Email Address or Token seeds may be entered during manual installation of FortiToken tokens, 3rd Party tokens, and Token Transfer.\n\nWhile FortiToken Mobile cannot change any settings without your permission, the following permissions are relevant to FortiToken Mobile operations:\n• Access to camera for scanning QR codes for easy token activation\n• TouchID/FaceID: used for app security, respectively.\n• Access to the Internet for communication to activate tokens and receive push notifications\n• \"Send Feedback by Email\", to automatically populate the \"Sender\" field\n• Internally share files between applications to prepare an attachment to be sent by email for \"Send Feedback by Email\"\n• FortiToken must keep the phone awake while it is upgrading the internal database to avoid data corruption.\n\nBy downloading and Installing FortiToken Mobile, I agree with all terms stated above.", "minimumOsVersion": "9.0", "primaryGenreName": "Business", "primaryGenreId": 6000, "isVppDeviceBasedLicensingEnabled": true, "userRatingCount": 108}