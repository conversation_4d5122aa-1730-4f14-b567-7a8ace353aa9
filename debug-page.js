const { fetchRankingData } = require('./list.js');

async function debugPage(pageNumber) {
    console.log(`🔍 调试第${pageNumber}页数据...`);
    
    try {
        const result = await fetchRankingData(pageNumber);
        
        console.log(`状态: ${result.success ? '成功' : '失败'}`);
        console.log(`HTTP状态码: ${result.status}`);
        
        if (result.success && result.data) {
            console.log('响应数据结构:');
            console.log('- code:', result.data.code);
            console.log('- msg:', result.data.msg);
            console.log('- rankInfo存在:', !!result.data.rankInfo);
            console.log('- rankInfo类型:', typeof result.data.rankInfo);
            
            if (result.data.rankInfo) {
                console.log('- rankInfo长度:', result.data.rankInfo.length);
                if (result.data.rankInfo.length > 0) {
                    console.log('- 第一个应用:', result.data.rankInfo[0].appInfo?.appName || '无名称');
                }
            }
            
            console.log('\n完整响应数据:');
            console.log(JSON.stringify(result.data, null, 2));
        } else {
            console.log('错误信息:', result.message);
            if (result.data) {
                console.log('错误数据:', JSON.stringify(result.data, null, 2));
            }
        }
        
    } catch (error) {
        console.error('调试异常:', error.message);
    }
}

// 调试第5页
debugPage(5);
