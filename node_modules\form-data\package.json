{"_from": "form-data@^4.0.0", "_id": "form-data@4.0.3", "_inBundle": false, "_integrity": "sha512-qsITQPfmvMOSAdeyZ+12I1c+CKSstAFAwu+97zrnWAbIr5u8wfsExUzCesVLC8NgHuRUqNN4Zy6UPWUTRGslcA==", "_location": "/form-data", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "form-data@^4.0.0", "name": "form-data", "escapedName": "form-data", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/axios"], "_resolved": "https://registry.npmmirror.com/form-data/-/form-data-4.0.3.tgz", "_shasum": "608b1b3f3e28be0fccf5901fc85fb3641e5cf0ae", "_spec": "form-data@^4.0.0", "_where": "D:\\node\\project\\automationTools\\node_modules\\axios", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "browser": "./lib/browser", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "bundleDependencies": false, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "hasown": "^2.0.2", "mime-types": "^2.1.12"}, "deprecated": false, "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "browserify": "^13.3.0", "browserify-istanbul": "^2.0.0", "coveralls": "^3.1.1", "cross-spawn": "^6.0.6", "eslint": "=8.8.0", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.2.6", "in-publish": "^2.0.1", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "pkgfiles": "^2.3.2", "request": "~2.87.0", "rimraf": "^2.7.1", "tape": "^5.9.0"}, "engines": {"node": ">= 6"}, "homepage": "https://github.com/form-data/form-data#readme", "license": "MIT", "main": "./lib/form_data", "name": "form-data", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "scripts": {"browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run tests-only && npm run browser && npm run report", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "lint": "eslint --ext=js,mjs .", "postpublish": "npm run restore-readme", "posttest": "npx npm@'>=10.2' audit --production", "posttests-only": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "npm run update-readme", "pretest": "npm run lint", "pretests-only": "rimraf coverage test/tmp", "report": "istanbul report lcov text", "restore-readme": "mv README.md.bak README.md", "test": "npm run tests-only", "tests-only": "istanbul cover test/run.js", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md"}, "typings": "./index.d.ts", "version": "4.0.3"}