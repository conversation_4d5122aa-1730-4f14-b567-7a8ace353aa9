const fs = require('fs').promises;
const path = require('path');

/**
 * 检查应用详细信息获取情况
 */
async function checkAppsInfo() {
    try {
        console.log('📊 检查应用详细信息获取情况...\n');
        
        // 读取 typeList.json 获取总应用数
        const typeListPath = path.join(__dirname, 'data', 'typeList.json');
        const typeListData = JSON.parse(await fs.readFile(typeListPath, 'utf8'));
        const totalApps = typeListData.rankInfo ? typeListData.rankInfo.length : 0;
        
        // 检查 appInfo 目录
        const appInfoDir = path.join(__dirname, 'data', 'appInfo');
        let appInfoFiles = [];
        
        try {
            appInfoFiles = await fs.readdir(appInfoDir);
            appInfoFiles = appInfoFiles.filter(file => file.endsWith('.json'));
        } catch (error) {
            console.log('❌ appInfo 目录不存在或为空');
            return;
        }
        
        console.log(`📱 总应用数: ${totalApps}`);
        console.log(`📁 已获取详细信息: ${appInfoFiles.length}`);
        console.log(`📈 完成进度: ${((appInfoFiles.length / totalApps) * 100).toFixed(1)}%`);
        console.log(`🔄 剩余待获取: ${totalApps - appInfoFiles.length}`);
        
        // 统计成功和失败的文件
        let successCount = 0;
        let failCount = 0;
        let totalSize = 0;
        const failedApps = [];
        
        console.log('\n🔍 分析详细信息文件...');
        
        for (const file of appInfoFiles) {
            try {
                const filePath = path.join(appInfoDir, file);
                const stats = await fs.stat(filePath);
                totalSize += stats.size;
                
                const content = JSON.parse(await fs.readFile(filePath, 'utf8'));
                
                if (content.success === false || content.error) {
                    failCount++;
                    failedApps.push({
                        appId: file.replace('.json', ''),
                        error: content.error || 'Unknown error'
                    });
                } else if (content.trackName || content.bundleId) {
                    successCount++;
                } else {
                    // 可能是成功但格式不同的文件
                    successCount++;
                }
            } catch (error) {
                console.log(`⚠️  文件 ${file} 读取失败: ${error.message}`);
                failCount++;
            }
        }
        
        console.log('\n📊 统计结果:');
        console.log(`✅ 成功获取: ${successCount} 个`);
        console.log(`❌ 获取失败: ${failCount} 个`);
        console.log(`📁 总文件大小: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
        console.log(`📄 平均文件大小: ${(totalSize / appInfoFiles.length / 1024).toFixed(2)} KB`);
        
        // 显示失败的应用
        if (failedApps.length > 0) {
            console.log('\n❌ 获取失败的应用:');
            failedApps.slice(0, 10).forEach((app, index) => {
                console.log(`${index + 1}. App ID: ${app.appId} - 错误: ${app.error}`);
            });
            
            if (failedApps.length > 10) {
                console.log(`... 还有 ${failedApps.length - 10} 个失败的应用`);
            }
        }
        
        // 显示一些成功获取的应用示例
        console.log('\n✅ 成功获取的应用示例:');
        let sampleCount = 0;
        
        for (const file of appInfoFiles.slice(0, 5)) {
            try {
                const filePath = path.join(appInfoDir, file);
                const content = JSON.parse(await fs.readFile(filePath, 'utf8'));
                
                if (content.trackName && !content.error) {
                    sampleCount++;
                    console.log(`${sampleCount}. ${content.trackName} (${content.artistName || 'Unknown'})`);
                    console.log(`   App ID: ${file.replace('.json', '')} | 分类: ${content.primaryGenreName || 'Unknown'}`);
                    console.log(`   版本: ${content.version || 'Unknown'} | 大小: ${content.fileSizeBytes ? (content.fileSizeBytes / 1024 / 1024).toFixed(1) + ' MB' : 'Unknown'}`);
                    console.log('');
                }
            } catch (error) {
                // 跳过错误文件
            }
        }
        
        // 给出建议
        console.log('💡 建议:');
        if (appInfoFiles.length < totalApps) {
            console.log(`- 运行 'node getAppsInfo.js' 继续获取剩余 ${totalApps - appInfoFiles.length} 个应用的详细信息`);
        }
        if (failCount > 0) {
            console.log(`- 有 ${failCount} 个应用获取失败，可能需要重试或检查网络连接`);
        }
        if (appInfoFiles.length === totalApps && failCount === 0) {
            console.log('- 🎉 所有应用详细信息已成功获取！');
        }
        
    } catch (error) {
        console.error('💥 检查过程中发生错误:', error.message);
    }
}

// 如果直接运行此文件，则执行检查函数
if (require.main === module) {
    checkAppsInfo();
}

module.exports = {
    checkAppsInfo
};
