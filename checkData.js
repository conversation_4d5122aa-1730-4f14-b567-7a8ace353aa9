const fs = require('fs');

// 读取数据文件
const data = JSON.parse(fs.readFileSync('./data/typeList.json', 'utf8'));

console.log('============================================================');
console.log('📊 数据统计报告');
console.log('============================================================');
console.log(`📈 总记录数: ${data.metadata.totalRecords}`);
console.log(`✅ 成功页数: ${data.metadata.successPages}`);
console.log(`❌ 失败页数: ${data.metadata.failedPages}`);
console.log(`📅 获取时间: ${new Date(data.metadata.fetchDate).toLocaleString('zh-CN')}`);

// 检查是否是多分类数据
if (data.metadata.totalGenres) {
    console.log(`📂 分类数量: ${data.metadata.totalGenres}`);
    console.log(`🏷️  包含分类: 多分类数据`);
} else if (data.metadata.genre) {
    console.log(`🏷️  应用类型: ${data.metadata.genreType || '单分类'} (genre: ${data.metadata.genre})`);
}

console.log(`🌍 国家: ${data.metadata.country.toUpperCase()}`);
console.log(`📱 设备: ${data.metadata.device}`);

console.log('\n🏆 前10个应用:');
data.rankInfo.slice(0, 10).forEach((app, i) => {
    console.log(`${i+1}. ${app.appName} (${app.publisher})`);
    console.log(`   App ID: ${app.appId} | 评分: ${app.rating} | 评论数: ${app.num.toLocaleString()}`);
    console.log(`   发布时间: ${app.lastReleaseTime}`);
});

// 检查是否有type字段（多分类数据）
const hasTypeField = data.rankInfo.length > 0 && data.rankInfo[0].type;

console.log('\n📊 数据结构信息:');
if (hasTypeField) {
    console.log(`   数据字段: lastReleaseTime, appId, appName, subtitle, publisher, icon, rating, num, type`);
    console.log(`   数据已优化: 移除app_id字段，处理subtitle空值，添加type字段`);

    // 显示分类统计
    const typeStats = {};
    data.rankInfo.forEach(app => {
        typeStats[app.type] = (typeStats[app.type] || 0) + 1;
    });

    console.log('\n📂 分类数据统计:');
    Object.entries(typeStats).forEach(([type, count]) => {
        console.log(`   ${type}: ${count} 条记录`);
    });
} else {
    console.log(`   数据字段: lastReleaseTime, appId, appName, subtitle, publisher, icon, rating, num`);
    console.log(`   数据已优化: 移除app_id字段，处理subtitle空值`);
}
console.log(`   评论数处理: "万"字已转换为实际数值`);

console.log('\n💰 评分最高的5个应用:');
const topRated = data.rankInfo
    .filter(app => app.rating > 0)
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 5);

topRated.forEach((app, i) => {
    console.log(`${i+1}. ${app.appName} - 评分: ${app.rating}`);
});

console.log('\n🔥 最受欢迎的5个应用 (按评论数):');
const topByComments = data.rankInfo
    .sort((a, b) => b.num - a.num)
    .slice(0, 5);

topByComments.forEach((app, i) => {
    console.log(`${i+1}. ${app.appName}`);
    console.log(`   发布商: ${app.publisher}`);
    console.log(`   评论数: ${app.num.toLocaleString()}`);
    console.log(`   评分: ${app.rating}`);
});

// 文件大小信息
const fileSize = fs.statSync('./data/typeList.json').size;
console.log('\n📁 文件信息:');
console.log(`   文件大小: ${(fileSize / 1024).toFixed(2)} KB`);
console.log(`   平均每条记录: ${(fileSize / data.rankInfo.length).toFixed(0)} 字节`);

console.log('\n============================================================');
console.log('数据检查完成 ✨');
console.log('============================================================');
