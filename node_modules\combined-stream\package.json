{"_from": "combined-stream@^1.0.8", "_id": "combined-stream@1.0.8", "_inBundle": false, "_integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "_location": "/combined-stream", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "combined-stream@^1.0.8", "name": "combined-stream", "escapedName": "combined-stream", "rawSpec": "^1.0.8", "saveSpec": null, "fetchSpec": "^1.0.8"}, "_requiredBy": ["/form-data"], "_resolved": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz", "_shasum": "c3d45a8b34fd730631a110a8a2520682b31d5a7f", "_spec": "combined-stream@^1.0.8", "_where": "D:\\node\\project\\automationTools\\node_modules\\form-data", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}, "bundleDependencies": false, "dependencies": {"delayed-stream": "~1.0.0"}, "deprecated": false, "description": "A stream that emits multiple other streams one after another.", "devDependencies": {"far": "~0.0.7"}, "engines": {"node": ">= 0.8"}, "homepage": "https://github.com/felixge/node-combined-stream", "license": "MIT", "main": "./lib/combined_stream", "name": "combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "scripts": {"test": "node test/run.js"}, "version": "1.0.8"}