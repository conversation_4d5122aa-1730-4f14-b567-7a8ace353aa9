require('dotenv').config();
const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');

/**
 * 从 iTunes API 获取应用详细信息
 * @param {string|number} appId - 应用ID
 * @returns {Promise<Object>} - 返回应用详细信息
 */
async function fetchAppInfo(appId) {
    const proxyUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(`https://itunes.apple.com/lookup?id=${appId}`)}`;
    
    try {
        console.log(`🔍 正在获取应用信息: ${appId}`);
        
        const response = await axios.get(proxyUrl, {
            timeout: 15000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36'
            }
        });
        
        if (response.data && response.data.contents) {
            const itunesData = JSON.parse(response.data.contents);
            
            if (itunesData.results && itunesData.results.length > 0) {
                console.log(`✅ 成功获取应用信息: ${itunesData.results[0].trackName}`);
                return {
                    success: true,
                    data: itunesData.results[0],
                    appId: appId
                };
            } else {
                console.log(`⚠️  应用 ${appId} 未找到详细信息`);
                return {
                    success: false,
                    error: 'No results found',
                    appId: appId
                };
            }
        } else {
            console.log(`❌ 应用 ${appId} 请求失败: 无效响应`);
            return {
                success: false,
                error: 'Invalid response',
                appId: appId
            };
        }
        
    } catch (error) {
        console.error(`❌ 应用 ${appId} 请求失败:`, error.message);
        return {
            success: false,
            error: error.message,
            appId: appId
        };
    }
}

/**
 * 保存应用信息到文件
 * @param {string|number} appId - 应用ID
 * @param {Object} appInfo - 应用信息
 */
async function saveAppInfo(appId, appInfo) {
    try {
        // 确保目录存在
        const appInfoDir = path.join(__dirname, 'data', 'appInfo');
        await fs.mkdir(appInfoDir, { recursive: true });
        
        // 保存文件
        const filePath = path.join(appInfoDir, `${appId}.json`);
        await fs.writeFile(filePath, JSON.stringify(appInfo, null, 2), 'utf8');
        
        console.log(`💾 应用 ${appId} 信息已保存到: ${filePath}`);
        return true;
    } catch (error) {
        console.error(`❌ 保存应用 ${appId} 信息失败:`, error.message);
        return false;
    }
}

/**
 * 读取 typeList.json 文件并提取所有 appId
 * @returns {Promise<Array>} - 返回 appId 数组
 */
async function loadAppIds() {
    try {
        const typeListPath = path.join(__dirname, 'data', 'typeList.json');
        const data = await fs.readFile(typeListPath, 'utf8');
        const typeListData = JSON.parse(data);
        
        if (typeListData.rankInfo && Array.isArray(typeListData.rankInfo)) {
            const appIds = typeListData.rankInfo.map(app => app.appId).filter(id => id);
            console.log(`📱 从 typeList.json 中找到 ${appIds.length} 个应用ID`);
            return appIds;
        } else {
            console.error('❌ typeList.json 格式不正确或缺少 rankInfo 数据');
            return [];
        }
    } catch (error) {
        console.error('❌ 读取 typeList.json 失败:', error.message);
        return [];
    }
}

/**
 * 检查应用信息文件是否已存在
 * @param {string|number} appId - 应用ID
 * @returns {Promise<boolean>} - 文件是否存在
 */
async function checkAppInfoExists(appId) {
    try {
        const filePath = path.join(__dirname, 'data', 'appInfo', `${appId}.json`);
        await fs.access(filePath);
        return true;
    } catch {
        return false;
    }
}

/**
 * 批量获取应用信息
 * @param {Array} appIds - 应用ID数组
 * @param {Object} options - 配置选项
 */
async function fetchAllAppsInfo(appIds, options = {}) {
    const {
        skipExisting = true,
        delay = 1000,
        batchSize = 10
    } = options;
    
    console.log('🚀 开始批量获取应用详细信息...');
    console.log(`📊 总共需要处理 ${appIds.length} 个应用`);
    console.log(`⚙️  配置: 跳过已存在=${skipExisting}, 延迟=${delay}ms, 批次大小=${batchSize}`);
    console.log('='.repeat(80));
    
    let successCount = 0;
    let skipCount = 0;
    let failCount = 0;
    
    for (let i = 0; i < appIds.length; i += batchSize) {
        const batch = appIds.slice(i, i + batchSize);
        console.log(`\n📦 处理批次 ${Math.floor(i / batchSize) + 1}/${Math.ceil(appIds.length / batchSize)} (${batch.length} 个应用)`);
        
        for (const appId of batch) {
            try {
                // 检查文件是否已存在
                if (skipExisting && await checkAppInfoExists(appId)) {
                    console.log(`⏭️  跳过已存在的应用: ${appId}`);
                    skipCount++;
                    continue;
                }
                
                // 获取应用信息
                const result = await fetchAppInfo(appId);
                
                // 保存应用信息
                if (result.success) {
                    const saved = await saveAppInfo(appId, result.data);
                    if (saved) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } else {
                    // 即使获取失败，也保存错误信息
                    await saveAppInfo(appId, {
                        error: result.error,
                        appId: appId,
                        fetchTime: new Date().toISOString(),
                        success: false
                    });
                    failCount++;
                }
                
                // 添加延迟避免请求过于频繁
                if (delay > 0) {
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
                
            } catch (error) {
                console.error(`💥 处理应用 ${appId} 时发生异常:`, error.message);
                failCount++;
            }
        }
        
        // 批次间添加额外延迟
        if (i + batchSize < appIds.length) {
            console.log(`⏳ 批次完成，等待 ${delay * 2}ms 后继续...`);
            await new Promise(resolve => setTimeout(resolve, delay * 2));
        }
    }
    
    console.log('\n' + '='.repeat(80));
    console.log('🎉 批量获取完成!');
    console.log(`✅ 成功: ${successCount} 个`);
    console.log(`⏭️  跳过: ${skipCount} 个`);
    console.log(`❌ 失败: ${failCount} 个`);
    console.log(`📊 总计: ${successCount + skipCount + failCount} 个`);
    console.log('='.repeat(80));
    
    return {
        success: true,
        total: appIds.length,
        successCount,
        skipCount,
        failCount
    };
}

/**
 * 主函数
 */
async function main() {
    try {
        // 读取应用ID列表
        const appIds = await loadAppIds();
        
        if (appIds.length === 0) {
            console.log('❌ 没有找到需要处理的应用ID');
            return;
        }
        
        // 批量获取应用信息
        const result = await fetchAllAppsInfo(appIds, {
            skipExisting: true,  // 跳过已存在的文件
            delay: 1000,         // 请求间隔1秒
            batchSize: 5         // 每批处理5个应用
        });
        
        if (result.success) {
            console.log('\n🎊 所有应用信息获取完成!');
        }
        
    } catch (error) {
        console.error('💥 程序执行异常:', error.message);
    }
}

// 如果直接运行此文件，则执行主函数
if (require.main === module) {
    main();
}

// 导出函数供其他模块使用
module.exports = {
    fetchAppInfo,
    saveAppInfo,
    loadAppIds,
    fetchAllAppsInfo,
    checkAppInfoExists,
    main
};
