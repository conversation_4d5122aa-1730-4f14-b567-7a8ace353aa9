{"screenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/Purple116/v4/c8/b3/38/c8b33898-b3d0-b0ae-2cea-99ad9be6496c/pr_source.jpg/392x696bb.jpg", "https://is1-ssl.mzstatic.com/image/thumb/Purple116/v4/65/87/6e/65876ea1-58e6-c2c8-0a7b-cd67d5819e80/pr_source.jpg/392x696bb.jpg", "https://is1-ssl.mzstatic.com/image/thumb/Purple116/v4/17/5d/c2/175dc26e-efe5-caba-228f-1eaf8ebbbb4d/pr_source.jpg/392x696bb.jpg", "https://is1-ssl.mzstatic.com/image/thumb/Purple116/v4/e0/5f/3b/e05f3b81-69e7-2410-ef72-5d7a6bd27362/pr_source.jpg/392x696bb.jpg", "https://is1-ssl.mzstatic.com/image/thumb/Purple126/v4/cb/09/31/cb093198-4699-67cf-dd58-2df38ed1163f/pr_source.jpg/392x696bb.jpg"], "ipadScreenshotUrls": ["https://is1-ssl.mzstatic.com/image/thumb/Purple126/v4/c2/d6/ea/c2d6ea56-07ed-1d4a-b0ed-8999175da3bd/mzl.hafzymxc.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple116/v4/2f/7e/d5/2f7ed5a8-0236-0a04-d76d-cd33c8b6c4d7/mzl.bdxtdfyn.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple126/v4/12/c3/e6/12c3e6a6-b4f1-a6a9-8071-b7a872c86eb5/mzl.dvgxdkle.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple126/v4/53/8f/a6/538fa6e9-f59c-fa8b-29eb-be445d26f4c9/mzl.ejffcwnw.png/576x768bb.png", "https://is1-ssl.mzstatic.com/image/thumb/Purple116/v4/9a/4c/fc/9a4cfc62-beef-a5c8-f9ea-2b739f57a374/mzl.rpuvkwxf.png/576x768bb.png"], "appletvScreenshotUrls": [], "artworkUrl512": "https://is1-ssl.mzstatic.com/image/thumb/Purple221/v4/0d/23/5e/0d235e86-5bc7-bae7-9543-e90dc94ab96e/AppIcon-0-0-1x_U007emarketing-0-8-0-0-85-220.png/512x512bb.jpg", "isGameCenterEnabled": false, "artistViewUrl": "https://apps.apple.com/us/developer/altametrics-inc/id728500230?uo=4", "artworkUrl60": "https://is1-ssl.mzstatic.com/image/thumb/Purple221/v4/0d/23/5e/0d235e86-5bc7-bae7-9543-e90dc94ab96e/AppIcon-0-0-1x_U007emarketing-0-8-0-0-85-220.png/60x60bb.jpg", "artworkUrl100": "https://is1-ssl.mzstatic.com/image/thumb/Purple221/v4/0d/23/5e/0d235e86-5bc7-bae7-9543-e90dc94ab96e/AppIcon-0-0-1x_U007emarketing-0-8-0-0-85-220.png/100x100bb.jpg", "features": ["iosUniversal"], "supportedDevices": ["iPhone5s-iPhone5s", "iPadAir-iPadAir", "iPadAirCellular-iPadAirCellular", "iPadMiniRetina-iPadMiniRetina", "iPadMiniRetinaCellular-iPadMiniRetinaCellular", "iPhone6-iPhone6", "iPhone6Plus-iPhone6Plus", "iPadAir2-iPadAir2", "iPadAir2Cellular-iPadAir2Cellular", "iPadMini3-iPadMini3", "iPadMini3Cellular-iPadMini3Cellular", "iPodTouchSixthGen-iPodTouchSixthGen", "iPhone6s-iPhone6s", "iPhone6sPlus-iPhone6sPlus", "iPadMini4-iPadMini4", "iPadMini4Cellular-iPadMini4Cellular", "iPadPro-iPadPro", "iPadProCellular-iPadProCellular", "iPadPro97-iPadPro97", "iPadPro97Cellular-iPadPro97Cellular", "iPhoneSE-iPhoneSE", "iPhone7-iPhone7", "iPhone7Plus-iPhone7Plus", "iPad611-iPad611", "iPad612-iPad612", "iPad71-iPad71", "iPad72-iPad72", "iPad73-iPad73", "iPad74-iPad74", "iPhone8-iPhone8", "iPhone8Plus-iPhone8Plus", "iPhoneX-iPhoneX", "iPad75-iPad75", "iPad76-iPad76", "iPhoneXS-iPhoneXS", "iPhoneXSMax-iPhoneXSMax", "iPhoneXR-iPhoneXR", "iPad812-iPad812", "iPad834-iPad834", "iPad856-iPad856", "iPad878-iPad878", "iPadMini5-iPadMini5", "iPadMini5Cellular-iPadMini5Cellular", "iPadAir3-iPadAir3", "iPadAir3Cellular-iPadAir3Cellular", "iPodTouchSeventhGen-iPodTouchSeventhGen", "iPhone11-iPhone11", "iPhone11Pro-iPhone11Pro", "iPadSeventhGen-iPadSeventhGen", "iPadSeventhGenCellular-iPadSeventhGenCellular", "iPhone11ProMax-iPhone11ProMax", "iPhoneSESecondGen-iPhoneSESecondGen", "iPadProSecondGen-iPadProSecondGen", "iPadProSecondGenCellular-iPadProSecondGenCellular", "iPadProFourthGen-iPadProFourthGen", "iPadProFourthGenCellular-iPadProFourthGenCellular", "iPhone12Mini-iPhone12Mini", "iPhone12-iPhone12", "iPhone12Pro-iPhone12Pro", "iPhone12ProMax-iPhone12ProMax", "iPadAir4-iPadAir4", "iPadAir4Cellular-iPadAir4Cellular", "iPadEighthGen-iPadEighthGen", "iPadEighthGenCellular-iPadEighthGenCellular", "iPadProThirdGen-iPadProThirdGen", "iPadProThirdGenCellular-iPadProThirdGenCellular", "iPadProFifthGen-iPadProFifthGen", "iPadProFifthGenCellular-iPadProFifthGenCellular", "iPhone13Pro-iPhone13Pro", "iPhone13ProMax-iPhone13ProMax", "iPhone13Mini-iPhone13Mini", "iPhone13-iPhone13", "iPadMiniSixthGen-iPadMiniSixthGen", "iPadMiniSixthGenCellular-iPadMiniSixthGenCellular", "iPadNinthGen-iPadNinthGen", "iPadNinthGenCellular-iPadNinthGenCellular", "iPhoneSEThirdGen-iPhoneSEThirdGen", "iPadAirFifthGen-iPadAirFifthGen", "iPadAirFifthGenCellular-iPadAirFifthGenCellular", "iPhone14-iPhone14", "iPhone14Plus-iPhone14Plus", "iPhone14Pro-iPhone14Pro", "iPhone14ProMax-iPhone14ProMax", "iPadTenthGen-iPadTenthGen", "iPadTenthGenCellular-iPadTenthGenCellular", "iPadPro11FourthGen-iPadPro11FourthGen", "iPadPro11FourthGenCellular-iPadPro11FourthGenCellular", "iPadProSixthGen-iPadProSixthGen", "iPadProSixthGenCellular-iPadProSixthGenCellular", "iPhone15-iPhone15", "iPhone15Plus-iPhone15Plus", "iPhone15Pro-iPhone15Pro", "iPhone15ProMax-iPhone15ProMax", "iPadAir11M2-iPadAir11M2", "iPadAir11M2Cellular-iPadAir11M2Cellular", "iPadAir13M2-iPadAir13M2", "iPadAir13M2Cellular-iPadAir13M2Cellular", "iPadPro11M4-iPadPro11M4", "iPadPro11M4Cellular-iPadPro11M4Cellular", "iPadPro13M4-iPadPro13M4", "iPadPro13M4Cellular-iPadPro13M4Cellular", "iPhone16-iPhone16", "iPhone16Plus-iPhone16Plus", "iPhone16Pro-iPhone16Pro", "iPhone16ProMax-iPhone16ProMax", "iPadMiniA17Pro-iPadMiniA17Pro", "iPadMiniA17ProCellular-iPadMiniA17ProCellular", "iPhone16e-iPhone16e", "iPadA16-iPadA16", "iPadA16Cellular-iPadA16Cellular", "iPadAir11M3-iPadAir11M3", "iPadAir11M3Cellular-iPadAir11M3Cellular", "iPadAir13M3-iPadAir13M3", "iPadAir13M3Cellular-iPadAir13M3Cellular"], "advisories": [], "kind": "software", "languageCodesISO2A": ["EN", "ES"], "fileSizeBytes": "71686144", "formattedPrice": "Free", "trackContentRating": "4+", "averageUserRatingForCurrentVersion": 4.66908, "contentAdvisoryRating": "4+", "averageUserRating": 4.66908, "trackCensoredName": "Altametrics Schedules", "trackViewUrl": "https://apps.apple.com/us/app/altametrics-schedules/id1207426322?uo=4", "artistId": 728500230, "artistName": "Altametrics Inc", "genres": ["Business"], "price": 0, "bundleId": "com.alta.altametricszipschedules", "releaseDate": "2017-02-22T11:48:15Z", "releaseNotes": "We regularly update the app to make it faster and more reliable. Here’s what’s new in the latest update:  \n\nWhat’s New?\n\n- Bug Fixes & Performance Enhancements\n - Resolved minor issues and improved overall performance.  \n\nWould you be enjoying the app? Leave us a review! Your feedback keeps Altametrics Schedules running smoothly.  \nNeed help?? Contact <NAME_EMAIL>", "trackId": 1207426322, "trackName": "Altametrics Schedules", "genreIds": ["6000"], "primaryGenreName": "Business", "primaryGenreId": 6000, "isVppDeviceBasedLicensingEnabled": true, "sellerName": "Altametrics Inc", "currentVersionReleaseDate": "2025-06-09T16:16:39Z", "version": "6.2", "wrapperType": "software", "currency": "USD", "description": "Altametrics Schedules puts the work schedule in your pocket. Requesting time-offs, swapping shifts, and editing availability is just a click away.\n\nFEATURES FOR MANAGERS:\nView historical, current, or future published/unpublished schedules.\nApprove or deny availability, time off, shift offer or accept requests.\nView all employee's schedules and contact information.\n\nFEATURES FOR EMPLOYEES:\nView work schedule anytime, anywhere.\nRequest Availability.\nRequest partial day or all day time off(s) and keep track of the request.\nFlexibility to offer/swap shifts with other employees.\nView coworkers' contact information depending on their preference whether to share information or not.\n\n\nACCOUNT: \n\nAltametrics works as a Business Management Tool primarily serving the e-restaurant industry, delivering its products in the form of SaaS. Currently, the Altametrics suite of applications comprises 4 applications running live on the App Store for our users to enjoy our services on the go.\n\nThe current Business Model of Altametrics gives flexibility to its users to join An Existing Business As An Employee.\n\nIn this use case, the user must fill in their email address and mobile number as a mandate to proceed, which is further stored in our database upon successful verification.", "minimumOsVersion": "12.0", "userRatingCountForCurrentVersion": 9824, "userRatingCount": 9824}