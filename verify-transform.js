const fs = require('fs');

// 读取转换后的数据
const transformedData = JSON.parse(fs.readFileSync('./data/type_transformed.json', 'utf8'));

console.log('🔍 数据转换验证报告');
console.log('='.repeat(60));

// 基本信息验证
console.log('\n📊 基本信息:');
console.log(`✅ 总记录数: ${transformedData.rankInfo.length}`);
console.log(`📅 转换时间: ${new Date(transformedData.metadata.transformDate).toLocaleString('zh-CN')}`);

// 字段结构验证
console.log('\n🔧 字段结构验证:');
const firstApp = transformedData.rankInfo[0];
const requiredFields = [
    'lastReleaseTime', 'appId', 'appName', 'subtitle', 
    'publisher', 'icon', 'rating', 'num'
];

requiredFields.forEach(field => {
    const exists = firstApp.hasOwnProperty(field);
    console.log(`   ${exists ? '✅' : '❌'} ${field}: ${exists ? '存在' : '缺失'}`);
});

// 评论数转换验证
console.log('\n💯 评论数转换验证:');
const wanExamples = transformedData.rankInfo
    .filter(app => app.num >= 10000)
    .slice(0, 10);

console.log('   包含"万"字转换的示例:');
wanExamples.forEach((app, i) => {
    console.log(`   ${i + 1}. ${app.appName}`);
    console.log(`      转换后评论数: ${app.num.toLocaleString()}`);
});

// 数据类型验证
console.log('\n🔢 数据类型验证:');
const typeChecks = {
    appId: 'number',
    appName: 'string',
    publisher: 'string',
    rating: 'number',
    num: 'number'
};

let typeErrors = 0;
Object.entries(typeChecks).forEach(([field, expectedType]) => {
    const actualType = typeof firstApp[field];
    const isCorrect = actualType === expectedType;
    if (!isCorrect) typeErrors++;
    console.log(`   ${isCorrect ? '✅' : '❌'} ${field}: ${actualType} (期望: ${expectedType})`);
});

// 数据完整性检查
console.log('\n📋 数据完整性检查:');
const completeness = {
    hasAppName: transformedData.rankInfo.filter(app => app.appName && app.appName !== '').length,
    hasPublisher: transformedData.rankInfo.filter(app => app.publisher && app.publisher !== '').length,
    hasIcon: transformedData.rankInfo.filter(app => app.icon && app.icon !== '').length,
    hasRating: transformedData.rankInfo.filter(app => app.rating > 0).length,
    hasComments: transformedData.rankInfo.filter(app => app.num > 0).length
};

Object.entries(completeness).forEach(([field, count]) => {
    const percentage = ((count / transformedData.rankInfo.length) * 100).toFixed(1);
    console.log(`   ${field}: ${count}/${transformedData.rankInfo.length} (${percentage}%)`);
});

// 评分分布
console.log('\n⭐ 评分分布:');
const ratingDistribution = {};
transformedData.rankInfo.forEach(app => {
    const rating = Math.floor(app.rating);
    ratingDistribution[rating] = (ratingDistribution[rating] || 0) + 1;
});

Object.keys(ratingDistribution)
    .sort((a, b) => b - a)
    .forEach(rating => {
        const count = ratingDistribution[rating];
        const percentage = ((count / transformedData.rankInfo.length) * 100).toFixed(1);
        console.log(`   ${rating}分: ${count} 个应用 (${percentage}%)`);
    });

// 评论数统计
console.log('\n💬 评论数统计:');
const commentRanges = {
    '1000万+': 0,
    '500万-1000万': 0,
    '100万-500万': 0,
    '10万-100万': 0,
    '1万-10万': 0,
    '1万以下': 0
};

transformedData.rankInfo.forEach(app => {
    const num = app.num;
    if (num >= 10000000) commentRanges['1000万+']++;
    else if (num >= 5000000) commentRanges['500万-1000万']++;
    else if (num >= 1000000) commentRanges['100万-500万']++;
    else if (num >= 100000) commentRanges['10万-100万']++;
    else if (num >= 10000) commentRanges['1万-10万']++;
    else commentRanges['1万以下']++;
});

Object.entries(commentRanges).forEach(([range, count]) => {
    const percentage = ((count / transformedData.rankInfo.length) * 100).toFixed(1);
    console.log(`   ${range}: ${count} 个应用 (${percentage}%)`);
});

// 最受欢迎的应用 (按评论数)
console.log('\n🏆 最受欢迎的应用 (按评论数):');
const topByComments = transformedData.rankInfo
    .sort((a, b) => b.num - a.num)
    .slice(0, 5);

topByComments.forEach((app, i) => {
    console.log(`   ${i + 1}. ${app.appName}`);
    console.log(`      发布商: ${app.publisher}`);
    console.log(`      评论数: ${app.num.toLocaleString()}`);
    console.log(`      评分: ${app.rating}`);
});

// 验证总结
console.log('\n' + '='.repeat(60));
if (typeErrors === 0) {
    console.log('✅ 数据转换验证通过！');
    console.log('🎉 所有字段类型正确，数据结构符合要求');
} else {
    console.log(`❌ 发现 ${typeErrors} 个类型错误，请检查数据转换逻辑`);
}
console.log('📁 转换后的数据文件: data/type_transformed.json');
console.log('='.repeat(60));
